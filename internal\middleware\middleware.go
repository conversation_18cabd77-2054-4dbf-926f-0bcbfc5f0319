package middleware

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/token"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// CORSMiddleware returns a CORS middleware with configurable allowed origins
func CORSMiddleware(allowedOrigins []string) gin.HandlerFunc {
	config := cors.DefaultConfig()

	if len(allowedOrigins) > 0 {
		config.AllowOrigins = allowedOrigins
	} else {
		// Default allowed origins - you can modify this list
		config.AllowOrigins = []string{
			"http://localhost:3000",
			"http://localhost:3001",
			"https://localhost:3000",
			"https://localhost:3001",
			"https://************",
			"http://localhost:3039",
			"http://**************:3039",
			"http://**************",
			"https://zac-admin-seven.vercel.app",
		}
	}

	// Configure allowed methods
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"}

	// Configure allowed headers
	config.AllowHeaders = []string{
		"Origin",
		"Content-Type",
		"Accept",
		"Authorization",
		"X-Requested-With",
	}

	// Allow credentials (cookies, authorization headers)
	config.AllowCredentials = true

	// Cache preflight requests for 12 hours
	config.MaxAge = 12 * time.Hour

	slog.Info("CORS middleware configured",
		"allowed_origins", config.AllowOrigins,
		"allowed_methods", config.AllowMethods,
		"allow_credentials", config.AllowCredentials,
	)

	return cors.New(config)
}

func JwtAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		clientIP := c.ClientIP()
		method := c.Request.Method
		path := c.Request.URL.Path

		slog.Debug("Authentication attempt",
			"method", method,
			"path", path,
			"client_ip", clientIP,
		)

		err := token.TokenValid(c)
		if err != nil {
			duration := time.Since(start)
			slog.Warn("Authentication failed",
				"method", method,
				"path", path,
				"client_ip", clientIP,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			c.String(http.StatusUnauthorized, "Unauthorized")
			c.Abort()
			return
		}

		duration := time.Since(start)
		slog.Debug("Authentication successful",
			"method", method,
			"path", path,
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		c.Next()
	}
}

// RequestLoggingMiddleware logs all HTTP requests with timing and response status
func RequestLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()
		userAgent := c.Request.UserAgent()

		slog.Info("Request started",
			"method", method,
			"path", path,
			"client_ip", clientIP,
			"user_agent", userAgent,
		)

		// Process request
		c.Next()

		// Log response
		duration := time.Since(start)
		statusCode := c.Writer.Status()
		responseSize := c.Writer.Size()

		logLevel := slog.LevelInfo
		if statusCode >= 400 && statusCode < 500 {
			logLevel = slog.LevelWarn
		} else if statusCode >= 500 {
			logLevel = slog.LevelError
		}

		slog.Log(c.Request.Context(), logLevel, "Request completed",
			"method", method,
			"path", path,
			"client_ip", clientIP,
			"status_code", statusCode,
			"duration_ms", duration.Milliseconds(),
			"response_size", responseSize,
		)
	}
}
