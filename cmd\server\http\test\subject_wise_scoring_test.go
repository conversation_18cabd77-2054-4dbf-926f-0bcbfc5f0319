package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestSubjectWiseScoringAndRanking(t *testing.T) {
	// Run migration to add question statistics columns
	migrations := []string{
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS total_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS attempted_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS unattempted_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS correct_answers INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS incorrect_answers INTEGER DEFAULT 0 NOT NULL",
	}

	for _, migration := range migrations {
		if err := db.Exec(migration).Error; err != nil {
			t.Logf("Migration warning: %s, error: %v", migration, err)
		}
	}

	// Use timestamp to ensure unique names across test runs
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano())
	testName := "SubjectWiseTest_" + timestamp
	mathSubjectName := "Mathematics_" + timestamp
	physicsSubjectName := "Physics_" + timestamp
	mathChapterName := "Algebra_" + timestamp
	physicsChapterName := "Mechanics_" + timestamp
	mathTopicName := "Linear_Equations_" + timestamp
	physicsTopicName := "Newtons_Laws_" + timestamp
	difficultyName := "Medium_" + timestamp
	mathSectionTypeName := "MathSection_" + timestamp
	physicsSectionTypeName := "PhysicsSection_" + timestamp
	testTypeName := "SubjectWiseTestType_" + timestamp

	// Step 1: Create test prerequisites
	// Create subjects
	mathSubject := models.Subject{Name: mathSubjectName, DisplayName: "Mathematics"}
	db.Create(&mathSubject)
	physicsSubject := models.Subject{Name: physicsSubjectName, DisplayName: "Physics"}
	db.Create(&physicsSubject)

	// Create chapters
	mathChapter := models.ChapterForCreate{
		Name:        mathChapterName,
		DisplayName: "Algebra",
		SubjectName: mathSubjectName,
	}
	mathChapterResp := requestExecutionHelper(http.MethodPost, "/api/chapters", mathChapter)
	assert.Equal(t, http.StatusOK, mathChapterResp.Code)

	physicsChapter := models.ChapterForCreate{
		Name:        physicsChapterName,
		DisplayName: "Mechanics",
		SubjectName: physicsSubjectName,
	}
	physicsChapterResp := requestExecutionHelper(http.MethodPost, "/api/chapters", physicsChapter)
	assert.Equal(t, http.StatusOK, physicsChapterResp.Code)

	// Create topics
	mathTopic := models.TopicForCreate{
		Name:        mathTopicName,
		ChapterName: mathChapterName,
	}
	mathTopicResp := requestExecutionHelper(http.MethodPost, "/api/topics", mathTopic)
	assert.Equal(t, http.StatusOK, mathTopicResp.Code)

	physicsTopic := models.TopicForCreate{
		Name:        physicsTopicName,
		ChapterName: physicsChapterName,
	}
	physicsTopicResp := requestExecutionHelper(http.MethodPost, "/api/topics", physicsTopic)
	assert.Equal(t, http.StatusOK, physicsTopicResp.Code)

	// Create difficulty
	difficulty := models.Difficulty{Name: difficultyName}
	db.Create(&difficulty)

	// Create section types with different scoring rules
	mathSectionType := models.SectionTypeForCreate{
		Name:          mathSectionTypeName,
		SubjectName:   mathSubjectName,
		QuestionCount: 2,
		PositiveMarks: 4.0, // +4 for correct answers
		NegativeMarks: 1.0, // -1 for incorrect answers
	}
	mathSectionTypeResp := requestExecutionHelper(http.MethodPost, "/api/section-types", mathSectionType)
	assert.Equal(t, http.StatusOK, mathSectionTypeResp.Code)

	physicsSectionType := models.SectionTypeForCreate{
		Name:          physicsSectionTypeName,
		SubjectName:   physicsSubjectName,
		QuestionCount: 2,
		PositiveMarks: 3.0, // +3 for correct answers
		NegativeMarks: 1.0, // -1 for incorrect answers
	}
	physicsSectionTypeResp := requestExecutionHelper(http.MethodPost, "/api/section-types", physicsSectionType)
	assert.Equal(t, http.StatusOK, physicsSectionTypeResp.Code)

	// Create test type
	testType := models.TestTypeForCreate{
		Name:             testTypeName,
		SectionTypeNames: []string{mathSectionTypeName, physicsSectionTypeName},
	}
	testTypeResp := requestExecutionHelper(http.MethodPost, "/api/test-types", testType)
	assert.Equal(t, http.StatusOK, testTypeResp.Code)

	// Create test
	test := models.TestForCreate{
		Name:         testName,
		TestTypeName: testTypeName,
		Description:  "Test for subject-wise scoring and ranking",
		FromTime:     time.Now(),
		ToTime:       time.Now().Add(2 * time.Hour),
	}
	testResp := requestExecutionHelper(http.MethodPost, "/api/tests", test)
	assert.Equal(t, http.StatusOK, testResp.Code)

	var createdTest models.Test
	err := json.Unmarshal(testResp.Body.Bytes(), &createdTest)
	assert.Nil(t, err)

	// Create questions for both subjects
	// Math questions
	mathQuestion1 := models.QuestionForCreate{
		Text:           "What is 2+2? " + timestamp,
		TopicName:      mathTopicName,
		DifficultyName: difficultyName,
		QuestionType:   "mcq",
		Options: []models.OptionForCreate{
			{OptionText: "3", IsCorrect: false},
			{OptionText: "4", IsCorrect: true},
			{OptionText: "5", IsCorrect: false},
		},
	}
	mathQ1Resp := requestExecutionHelper(http.MethodPost, "/api/questions", mathQuestion1)
	assert.Equal(t, http.StatusOK, mathQ1Resp.Code)
	var createdMathQ1 models.SimpleEntityResponse
	err = json.Unmarshal(mathQ1Resp.Body.Bytes(), &createdMathQ1)
	assert.Nil(t, err)

	mathQuestion2 := models.QuestionForCreate{
		Text:           "What is 3+3? " + timestamp,
		TopicName:      mathTopicName,
		DifficultyName: difficultyName,
		QuestionType:   "text",
		CorrectAnswer:  "6",
	}
	mathQ2Resp := requestExecutionHelper(http.MethodPost, "/api/questions", mathQuestion2)
	assert.Equal(t, http.StatusOK, mathQ2Resp.Code)
	var createdMathQ2 models.SimpleEntityResponse
	err = json.Unmarshal(mathQ2Resp.Body.Bytes(), &createdMathQ2)
	assert.Nil(t, err)

	// Physics questions
	physicsQuestion1 := models.QuestionForCreate{
		Text:           "What is Newton's first law? " + timestamp,
		TopicName:      physicsTopicName,
		DifficultyName: difficultyName,
		QuestionType:   "text",
		CorrectAnswer:  "Inertia",
	}
	physicsQ1Resp := requestExecutionHelper(http.MethodPost, "/api/questions", physicsQuestion1)
	assert.Equal(t, http.StatusOK, physicsQ1Resp.Code)
	var createdPhysicsQ1 models.SimpleEntityResponse
	err = json.Unmarshal(physicsQ1Resp.Body.Bytes(), &createdPhysicsQ1)
	assert.Nil(t, err)

	physicsQuestion2 := models.QuestionForCreate{
		Text:           "What is the unit of force? " + timestamp,
		TopicName:      physicsTopicName,
		DifficultyName: difficultyName,
		QuestionType:   "text",
		CorrectAnswer:  "Newton",
	}
	physicsQ2Resp := requestExecutionHelper(http.MethodPost, "/api/questions", physicsQuestion2)
	assert.Equal(t, http.StatusOK, physicsQ2Resp.Code)
	var createdPhysicsQ2 models.SimpleEntityResponse
	err = json.Unmarshal(physicsQ2Resp.Body.Bytes(), &createdPhysicsQ2)
	assert.Nil(t, err)

	// Add questions to test sections (use actual section names generated during test creation)
	mathSectionName := fmt.Sprintf("%s_section_A_%d", testName, createdTest.ID)
	physicsSectionName := fmt.Sprintf("%s_section_B_%d", testName, createdTest.ID)

	mathQuestionsRequest := models.AddQuestionsToTestRequest{
		SectionName: mathSectionName,
		QuestionIDs: []uint{createdMathQ1.ID, createdMathQ2.ID},
	}
	mathQuestionsResp := requestExecutionHelper(http.MethodPost, fmt.Sprintf("/api/tests/%d/questions", createdTest.ID), mathQuestionsRequest)
	assert.Equal(t, http.StatusOK, mathQuestionsResp.Code)

	physicsQuestionsRequest := models.AddQuestionsToTestRequest{
		SectionName: physicsSectionName,
		QuestionIDs: []uint{createdPhysicsQ1.ID, createdPhysicsQ2.ID},
	}
	physicsQuestionsResp := requestExecutionHelper(http.MethodPost, fmt.Sprintf("/api/tests/%d/questions", createdTest.ID), physicsQuestionsRequest)
	assert.Equal(t, http.StatusOK, physicsQuestionsResp.Code)

	// Activate the test so students can submit responses
	activateRequest := map[string]bool{"active": true}
	activateResp := requestExecutionHelper(http.MethodPut, fmt.Sprintf("/api/tests/%d/active", createdTest.ID), activateRequest)
	assert.Equal(t, http.StatusOK, activateResp.Code)

	// Step 2: Create students and record test responses
	// Student 1: Good at Math, Poor at Physics
	student1 := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Math Student " + timestamp,
			Email:          "mathstudent" + timestamp + "@example.com",
			PhoneNumber:    "1234567890" + timestamp[len(timestamp)-3:],
			ContactAddress: "Test Address",
		},
		ParentPhone: "9876543210" + timestamp[len(timestamp)-3:],
		ParentEmail: "mathparent" + timestamp + "@example.com",
		Institute:   "Test Institute",
		Class:       "12th",
		Stream:      "IIT-JEE",
		CityOrTown:  "Test City",
		State:       "Test State",
		Password:    "testpassword123",
	}
	student1Resp := requestExecutionHelper(http.MethodPost, "/api/students", student1)
	assert.Equal(t, http.StatusOK, student1Resp.Code)
	var createdStudent1 models.CreatedStudentResponse
	err = json.Unmarshal(student1Resp.Body.Bytes(), &createdStudent1)
	assert.Nil(t, err)

	// Student 2: Good at Physics, Poor at Math
	student2 := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Physics Student " + timestamp,
			Email:          "physicsstudent" + timestamp + "@example.com",
			PhoneNumber:    "1234567891" + timestamp[len(timestamp)-3:],
			ContactAddress: "Test Address",
		},
		ParentPhone: "9876543211" + timestamp[len(timestamp)-3:],
		ParentEmail: "physicsparent" + timestamp + "@example.com",
		Institute:   "Test Institute",
		Class:       "12th",
		Stream:      "IIT-JEE",
		CityOrTown:  "Test City",
		State:       "Test State",
		Password:    "testpassword123",
	}
	student2Resp := requestExecutionHelper(http.MethodPost, "/api/students", student2)
	assert.Equal(t, http.StatusOK, student2Resp.Code)
	var createdStudent2 models.CreatedStudentResponse
	err = json.Unmarshal(student2Resp.Body.Bytes(), &createdStudent2)
	assert.Nil(t, err)

	// Step 3: Record test responses
	// Student 1 responses: Correct Math, Incorrect Physics
	student1Responses := models.TestResponsesForCreate{
		TestID: createdTest.ID,
		Responses: []models.TestResponseForCreate{
			{
				QuestionID:        createdMathQ1.ID,
				SelectedOptionIDs: []int{}, // Will need to get correct option ID
				ResponseText:      nil,
			},
			{
				QuestionID:   createdMathQ2.ID,
				ResponseText: func() *string { s := "6"; return &s }(), // Correct answer
			},
			{
				QuestionID:   createdPhysicsQ1.ID,
				ResponseText: func() *string { s := "Wrong"; return &s }(), // Incorrect answer
			},
			{
				QuestionID:   createdPhysicsQ2.ID,
				ResponseText: func() *string { s := "Wrong"; return &s }(), // Incorrect answer
			},
		},
	}

	// Record Student 1 responses
	student1RecordResp := authenticatedRequestHelper(http.MethodPost, "/api/test-responses", student1Responses, createdStudent1.Token)
	assert.Equal(t, http.StatusOK, student1RecordResp.Code)

	// Student 2 responses: Incorrect Math, Correct Physics
	student2Responses := models.TestResponsesForCreate{
		TestID: createdTest.ID,
		Responses: []models.TestResponseForCreate{
			{
				QuestionID:        createdMathQ1.ID,
				SelectedOptionIDs: []int{}, // Will select wrong option
				ResponseText:      nil,
			},
			{
				QuestionID:   createdMathQ2.ID,
				ResponseText: func() *string { s := "Wrong"; return &s }(), // Incorrect answer
			},
			{
				QuestionID:   createdPhysicsQ1.ID,
				ResponseText: func() *string { s := "Inertia"; return &s }(), // Correct answer
			},
			{
				QuestionID:   createdPhysicsQ2.ID,
				ResponseText: func() *string { s := "Newton"; return &s }(), // Correct answer
			},
		},
	}

	// Record Student 2 responses
	student2RecordResp := authenticatedRequestHelper(http.MethodPost, "/api/test-responses", student2Responses, createdStudent2.Token)
	assert.Equal(t, http.StatusOK, student2RecordResp.Code)

	// Step 4: Verify StudentTestMark records with subject-wise scores
	var student1TestMark models.StudentTestMark
	err = db.Where("student_id = ? AND test_id = ?", createdStudent1.Student.ID, createdTest.ID).First(&student1TestMark).Error
	assert.Nil(t, err, "Student1 TestMark record should be created")

	var student2TestMark models.StudentTestMark
	err = db.Where("student_id = ? AND test_id = ?", createdStudent2.Student.ID, createdTest.ID).First(&student2TestMark).Error
	assert.Nil(t, err, "Student2 TestMark record should be created")

	// Verify subject-wise scores are stored
	assert.NotNil(t, student1TestMark.SubjectMarks, "Student1 should have subject marks")
	assert.NotNil(t, student2TestMark.SubjectMarks, "Student2 should have subject marks")

	// Verify Math scores
	mathMarks1, hasMath1 := student1TestMark.SubjectMarks[mathSubjectName]
	assert.True(t, hasMath1, "Student1 should have Math marks")
	mathMarks2, hasMath2 := student2TestMark.SubjectMarks[mathSubjectName]
	assert.True(t, hasMath2, "Student2 should have Math marks")

	// Verify Physics scores
	physicsMarks1, hasPhysics1 := student1TestMark.SubjectMarks[physicsSubjectName]
	assert.True(t, hasPhysics1, "Student1 should have Physics marks")
	physicsMarks2, hasPhysics2 := student2TestMark.SubjectMarks[physicsSubjectName]
	assert.True(t, hasPhysics2, "Student2 should have Physics marks")

	// Step 5: Test overall rankings with subject-wise data
	rankingsResp := authenticatedRequestHelper(http.MethodGet, fmt.Sprintf("/api/tests/rankings/%d", createdTest.ID), nil, createdStudent1.Token)
	assert.Equal(t, http.StatusOK, rankingsResp.Code)

	var rankingsResult models.TestRankingResult
	err = json.Unmarshal(rankingsResp.Body.Bytes(), &rankingsResult)
	assert.Nil(t, err)

	// Verify rankings include subject-wise data
	assert.Equal(t, 2, rankingsResult.TotalStudents)
	assert.Equal(t, 2, len(rankingsResult.StudentRankings))

	// Verify each student has subject ranks
	for _, ranking := range rankingsResult.StudentRankings {
		assert.NotNil(t, ranking.SubjectRanks, "Student should have subject ranks")
		assert.True(t, len(ranking.SubjectRanks) > 0, "Student should have at least one subject rank")
	}

	t.Logf("✅ Subject-wise scoring and ranking test completed successfully")
	t.Logf("Student1 Math: +%d/-%d, Physics: +%d/-%d",
		mathMarks1.PositiveMarks, mathMarks1.NegativeMarks,
		physicsMarks1.PositiveMarks, physicsMarks1.NegativeMarks)
	t.Logf("Student2 Math: +%d/-%d, Physics: +%d/-%d",
		mathMarks2.PositiveMarks, mathMarks2.NegativeMarks,
		physicsMarks2.PositiveMarks, physicsMarks2.NegativeMarks)
}
