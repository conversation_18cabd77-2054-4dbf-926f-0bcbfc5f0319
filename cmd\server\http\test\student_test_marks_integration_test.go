package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestStudentTestMarksIntegrationFlow(t *testing.T) {
	// Run migration to add question statistics columns and eligible_for_ranking
	migrations := []string{
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS total_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS attempted_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS unattempted_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS correct_answers INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS incorrect_answers INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS eligible_for_ranking BOOLEAN DEFAULT FALSE NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_student_test_marks_eligible_ranking ON student_test_marks (test_id, eligible_for_ranking, final_marks DESC) WHERE eligible_for_ranking = TRUE",
		"ALTER TABLE tests ADD COLUMN IF NOT EXISTS duration INTEGER",
	}

	for _, migration := range migrations {
		if err := db.Exec(migration).Error; err != nil {
			t.Logf("Migration warning: %s, error: %v", migration, err)
		}
	}

	// Re-enable this test to verify subject-wise scoring works
	// Use timestamp to ensure unique names across test runs
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano())
	courseName := "TestCourse_" + timestamp
	testName := "TestMark_" + timestamp
	subjectName := "TestSubject_" + timestamp
	sectionTypeName := "TestSection_" + timestamp
	testTypeName := "TestType_" + timestamp
	chapterName := "TestChapter_" + timestamp
	topicName := "TestTopic_" + timestamp
	difficultyName := "TestDifficulty_" + timestamp

	// Clean up before test
	defer func() {
		db.Exec("DELETE FROM student_test_marks WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM sections_questions WHERE section_id IN (SELECT id FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?))", testName)
		db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM courses_tests WHERE course_id IN (SELECT id FROM courses WHERE name = ?)", courseName)
		db.Exec("DELETE FROM tests WHERE name = ?", testName)
		db.Exec("DELETE FROM test_type_section_types WHERE test_type_id IN (SELECT id FROM test_types WHERE name = ?)", testTypeName)
		db.Exec("DELETE FROM test_types WHERE name = ?", testTypeName)
		db.Exec("DELETE FROM section_types WHERE name = ?", sectionTypeName)
		db.Exec("DELETE FROM options WHERE question_id IN (SELECT id FROM questions WHERE text LIKE ?)", "%"+timestamp+"%")
		db.Exec("DELETE FROM questions WHERE text LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM topics WHERE name = ?", topicName)
		db.Exec("DELETE FROM chapters WHERE name = ?", chapterName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
		db.Exec("DELETE FROM difficulties WHERE name = ?", difficultyName)
		db.Exec("DELETE FROM courses WHERE name = ?", courseName)
	}()

	// Step 1: Create all required entities
	// Create subject
	subject := models.Subject{
		Name:        subjectName,
		DisplayName: subjectName,
	}
	subjectResp := requestExecutionHelper(http.MethodPost, "/api/subjects", subject)
	assert.Equal(t, http.StatusOK, subjectResp.Code)

	// Create chapter
	chapter := models.ChapterForCreate{
		Name:        chapterName,
		DisplayName: chapterName,
		SubjectName: subjectName,
	}
	chapterResp := requestExecutionHelper(http.MethodPost, "/api/chapters", chapter)
	assert.Equal(t, http.StatusOK, chapterResp.Code)

	// Create topic
	topic := models.TopicForCreate{
		Name:        topicName,
		ChapterName: chapterName,
	}
	topicResp := requestExecutionHelper(http.MethodPost, "/api/topics", topic)
	assert.Equal(t, http.StatusOK, topicResp.Code)

	// Create difficulty
	difficulty := models.Difficulty{Name: difficultyName}
	db.Create(&difficulty)

	// Create section type with specific positive/negative marks
	sectionType := models.SectionTypeForCreate{
		Name:          sectionTypeName,
		SubjectName:   subjectName,
		QuestionCount: 2,
		PositiveMarks: 4.0, // +4 for correct answers
		NegativeMarks: 1.0, // -1 for incorrect answers
	}
	sectionTypeResp := requestExecutionHelper(http.MethodPost, "/api/section-types", sectionType)
	assert.Equal(t, http.StatusOK, sectionTypeResp.Code)

	// Create test type
	testType := models.TestTypeForCreate{
		Name:             testTypeName,
		SectionTypeNames: []string{sectionTypeName},
	}
	testTypeResp := requestExecutionHelper(http.MethodPost, "/api/test-types", testType)
	assert.Equal(t, http.StatusOK, testTypeResp.Code)

	// Create test with duration
	duration := 60 // 60 minutes
	test := models.TestForCreate{
		Name:         testName,
		TestTypeName: testTypeName,
		Description:  "Integration test for StudentTestMark creation",
		FromTime:     time.Now(),
		ToTime:       time.Now().Add(2 * time.Hour),
		Duration:     &duration,
	}
	testResp := requestExecutionHelper(http.MethodPost, "/api/tests", test)
	assert.Equal(t, http.StatusOK, testResp.Code)

	var createdTest models.Test
	err := json.Unmarshal(testResp.Body.Bytes(), &createdTest)
	assert.Nil(t, err)

	// Create MCQ question with options
	mcqQuestion := models.QuestionForCreate{
		Text:           "What is 2+2? " + timestamp,
		TopicName:      topicName,
		DifficultyName: difficultyName,
		QuestionType:   "mcq",
		Options: []models.OptionForCreate{
			{OptionText: "3", IsCorrect: false},
			{OptionText: "4", IsCorrect: true},
			{OptionText: "5", IsCorrect: false},
		},
	}
	mcqResp := requestExecutionHelper(http.MethodPost, "/api/questions", mcqQuestion)
	assert.Equal(t, http.StatusOK, mcqResp.Code)

	var createdMCQQuestion models.SimpleEntityResponse
	err = json.Unmarshal(mcqResp.Body.Bytes(), &createdMCQQuestion)
	assert.Nil(t, err)

	// Get the question details to find the correct option ID
	var mcqQuestionFromDB models.Question
	err = db.Preload("Options").Where("id = ?", createdMCQQuestion.ID).First(&mcqQuestionFromDB).Error
	assert.Nil(t, err)

	var correctOptionID int
	for _, option := range mcqQuestionFromDB.Options {
		if option.IsCorrect {
			correctOptionID = int(option.ID)
			break
		}
	}

	// Create text question
	textQuestion := models.QuestionForCreate{
		Text:           "What is the capital of France? " + timestamp,
		TopicName:      topicName,
		DifficultyName: difficultyName,
		QuestionType:   "text",
		CorrectAnswer:  "Paris",
	}
	textResp := requestExecutionHelper(http.MethodPost, "/api/questions", textQuestion)
	assert.Equal(t, http.StatusOK, textResp.Code)

	var createdTextQuestion models.SimpleEntityResponse
	err = json.Unmarshal(textResp.Body.Bytes(), &createdTextQuestion)
	assert.Nil(t, err)

	// Find the actual section name that was created (sections are auto-generated with pattern: testName_section_A_testID)
	actualSectionName := fmt.Sprintf("%s_section_A_%d", testName, createdTest.ID)

	// Add questions to test
	addQuestionsRequest := models.AddQuestionsToTestRequest{
		SectionName: actualSectionName,
		QuestionIDs: []uint{createdMCQQuestion.ID, createdTextQuestion.ID},
	}
	addQuestionsResp := requestExecutionHelper(http.MethodPost, fmt.Sprintf("/api/tests/%d/questions", createdTest.ID), addQuestionsRequest)
	assert.Equal(t, http.StatusOK, addQuestionsResp.Code)

	// Activate the test
	activateResp := requestExecutionHelper(http.MethodPut, fmt.Sprintf("/api/tests/%d/active", createdTest.ID), nil)
	assert.Equal(t, http.StatusOK, activateResp.Code)

	// Step 2: Create a student and record test responses
	student := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Test Student " + timestamp,
			Email:          "teststudent" + timestamp + "@example.com",
			PhoneNumber:    "1234567890",
			ContactAddress: "Test Address",
		},
		ParentPhone: "9876543210",
		ParentEmail: "parent" + timestamp + "@example.com",
		Password:    "testpassword123",
	}
	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", student)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var createdStudentResponse models.CreatedStudentResponse
	err = json.Unmarshal(studentResp.Body.Bytes(), &createdStudentResponse)
	assert.Nil(t, err)

	// Record test responses (1 correct MCQ, 1 incorrect text)
	testResponses := models.TestResponsesForCreate{
		TestID: createdTest.ID,
		Responses: []models.TestResponseForCreate{
			{
				QuestionID:        createdMCQQuestion.ID,
				SelectedOptionIDs: []int{}, // We need to get the correct option ID
				ResponseText:      nil,
			},
			{
				QuestionID:        createdTextQuestion.ID,
				SelectedOptionIDs: []int{},
				ResponseText:      func() *string { s := "London"; return &s }(), // Incorrect answer
			},
		},
	}

	// Use the correct option ID we found earlier
	testResponses.Responses[0].SelectedOptionIDs = []int{correctOptionID}

	// We can use the token from student creation response directly
	// The CreateStudent API already returns a token
	loginToken := createdStudentResponse.Token

	// Record responses with authentication (responses are now evaluated immediately)
	recordResp := authenticatedRequestHelper(http.MethodPost, "/api/test-responses", testResponses, loginToken)
	assert.Equal(t, http.StatusOK, recordResp.Code)

	// Verify the response includes evaluation results
	var recordResult models.TestResponsesResult
	err = json.Unmarshal(recordResp.Body.Bytes(), &recordResult)
	assert.Nil(t, err)

	// Verify evaluation results in the record response
	assert.Equal(t, 2, recordResult.TotalQuestions)
	assert.Equal(t, 1, recordResult.CorrectAnswers) // 1 correct MCQ
	assert.Equal(t, 3, recordResult.TotalScore)     // +4 for correct MCQ, -1 for incorrect text = 3

	// Step 3: Verify StudentTestMark record was created
	var studentTestMark models.StudentTestMark
	err = db.Where("student_id = ? AND test_id = ?", createdStudentResponse.Student.ID, createdTest.ID).First(&studentTestMark).Error
	assert.Nil(t, err, "StudentTestMark record should be created")

	assert.Equal(t, int(createdStudentResponse.Student.ID), studentTestMark.StudentID)
	assert.Equal(t, int(createdTest.ID), studentTestMark.TestID)
	assert.Equal(t, 4, studentTestMark.TotalPositiveMarks) // +4 for correct MCQ
	assert.Equal(t, 1, studentTestMark.TotalNegativeMarks) // +1 for incorrect text (stored as positive)
	// Final marks should be calculated by database as positive - negative = 4 - 1 = 3

	// Verify eligible_for_ranking is set correctly (test has valid timing)
	assert.True(t, studentTestMark.EligibleForRanking, "Marks should be eligible for ranking since test has valid timing")

	// Step 4: Verify subject-wise scores are stored
	assert.NotNil(t, studentTestMark.SubjectMarks, "Subject marks should be stored")
	assert.True(t, len(studentTestMark.SubjectMarks) > 0, "Should have at least one subject")

	// Log subject-wise breakdown for debugging
	for subjectName, subjectMark := range studentTestMark.SubjectMarks {
		t.Logf("Subject %s: +%d/-%d, Topics: %d",
			subjectName, subjectMark.PositiveMarks, subjectMark.NegativeMarks, len(subjectMark.TopicMarks))
		for topicName, topicMark := range subjectMark.TopicMarks {
			t.Logf("  Topic %s: +%d/-%d", topicName, topicMark.PositiveMarks, topicMark.NegativeMarks)
		}
	}

	// Step 5: Test GetTestRankings API
	rankingsResp := authenticatedRequestHelper(http.MethodGet, fmt.Sprintf("/api/tests/rankings/%d", createdTest.ID), nil, loginToken)
	assert.Equal(t, http.StatusOK, rankingsResp.Code)

	var rankingsResult models.TestRankingResult
	err = json.Unmarshal(rankingsResp.Body.Bytes(), &rankingsResult)
	assert.Nil(t, err)

	// Verify rankings
	assert.Equal(t, createdTest.ID, rankingsResult.TestID)
	assert.Equal(t, testName, rankingsResult.TestName)
	assert.Equal(t, 1, rankingsResult.TotalStudents)
	assert.Equal(t, 1, len(rankingsResult.StudentRankings))

	ranking := rankingsResult.StudentRankings[0]
	assert.Equal(t, createdStudentResponse.Student.ID, ranking.StudentID)
	assert.Equal(t, 4, ranking.TotalPositiveMarks)
	assert.Equal(t, 1, ranking.TotalNegativeMarks)
	assert.Equal(t, 3, ranking.FinalMarks)     // 4 - 1 = 3
	assert.Equal(t, 1, ranking.Rank)           // Should be rank 1
	assert.Equal(t, 100.0, ranking.Percentile) // 100% since only one student

	// Step 6: Verify subject-wise rankings are included
	assert.NotNil(t, ranking.SubjectRanks, "Subject ranks should be included")
	if len(ranking.SubjectRanks) > 0 {
		for subjectName, subjectRank := range ranking.SubjectRanks {
			assert.Equal(t, createdStudentResponse.Student.ID, subjectRank.StudentID)
			assert.Equal(t, 1, subjectRank.Rank)           // Should be rank 1 in each subject (only one student)
			assert.Equal(t, 100.0, subjectRank.Percentile) // 100% since only one student
			t.Logf("Subject %s rank: %d, percentile: %.1f%%", subjectName, subjectRank.Rank, subjectRank.Percentile)
		}
	}

	t.Logf("✅ Integration test passed! StudentTestMark created with positive=%d, negative=%d, final=%d",
		studentTestMark.TotalPositiveMarks, studentTestMark.TotalNegativeMarks, ranking.FinalMarks)
}
