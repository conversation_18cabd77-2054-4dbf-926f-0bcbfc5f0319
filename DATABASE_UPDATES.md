# Database Schema Updates for Subject-Wise Scoring

## Overview

This document outlines the database schema updates required to support the enhanced subject-wise scoring and ranking functionality implemented in the ZIA Academy backend.

## What's New

### ✅ Enhanced Features
- **Subject-wise scoring**: Track student performance by subject and topic
- **Question statistics tracking**: Monitor attempted, unattempted, correct, and incorrect answers
- **Flexible scoring rules**: Section types define positive/negative marks
- **Optimized rankings**: Fast test-specific ranking queries
- **JSONB storage**: Efficient hierarchical data storage for subject breakdowns with question stats
- **Generated columns**: Automatic final mark calculations
- **Comprehensive analytics**: Overall and subject-wise question attempt patterns

## Files Updated

### 1. Schema Files
- **`schemas/schemas.sql`** - Updated main schema with optimized indexes
- **`migrations/enhance_subject_wise_scoring_schema.sql`** - Comprehensive migration script
- **`docs/subject_wise_scoring_schema.md`** - Detailed schema documentation

### 2. Verification Scripts
- **`scripts/verify_subject_scoring_schema.sql`** - Schema verification script

## Key Schema Changes

### 1. `student_test_marks` Table Enhancements
```sql
-- Enhanced with JSONB subject_marks column and optimized indexes
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS subject_marks JSONB;
CREATE INDEX idx_student_test_marks_subject_marks_gin ON student_test_marks USING GIN (subject_marks);
```

### 2. `test_responses` Table Updates
```sql
-- Added evaluation result columns
ALTER TABLE test_responses ADD COLUMN IF NOT EXISTS calculated_score INTEGER;
ALTER TABLE test_responses ADD COLUMN IF NOT EXISTS is_correct BOOLEAN DEFAULT FALSE;
```

### 3. New Indexes for Performance
```sql
-- Optimized for ranking and subject-wise queries
CREATE INDEX idx_test_final_marks_desc ON student_test_marks (test_id, final_marks DESC);
CREATE INDEX idx_student_test_marks_student_test ON student_test_marks (student_id, test_id);
CREATE INDEX idx_test_responses_student_test ON test_responses (student_id, test_id);
```

## Migration Instructions

### Option 1: Run Comprehensive Migration
```bash
# Apply all subject-wise scoring enhancements
psql -d your_database_name -f migrations/enhance_subject_wise_scoring_schema.sql
```

### Option 2: Apply Individual Migrations (if needed)
```bash
# Apply existing migrations in order
psql -d your_database_name -f migrations/add_subject_marks_to_student_test_marks.sql
psql -d your_database_name -f migrations/add_subject_marks_gin_index.sql
```

### Verification
```bash
# Verify the schema updates
psql -d your_database_name -f scripts/verify_subject_scoring_schema.sql
```

## Data Structure

### Subject Marks JSONB Format
```json
{
  "Mathematics": {
    "positive_marks": 12,
    "negative_marks": 3,
    "topic_marks": {
      "Algebra": {"positive_marks": 8, "negative_marks": 1},
      "Geometry": {"positive_marks": 4, "negative_marks": 2}
    }
  },
  "Physics": {
    "positive_marks": 8,
    "negative_marks": 2,
    "topic_marks": {
      "Mechanics": {"positive_marks": 8, "negative_marks": 2}
    }
  }
}
```

## Performance Benefits

### 1. Fast Rankings
- **Before**: Sequential scan for rankings
- **After**: Index-optimized `O(log n)` ranking queries

### 2. Efficient Subject Queries
- **GIN Index**: Supports complex JSONB queries
- **Operators**: `?`, `@>`, `->`, `->>`, `#>` all optimized

### 3. Reduced Computation
- **Generated Columns**: Automatic final mark calculation
- **Materialized Data**: Subject breakdowns pre-calculated

## API Impact

### Enhanced Endpoints
- `POST /api/test-responses` - Now calculates and stores subject-wise scores
- `GET /api/tests/rankings/:test_id` - Returns subject-wise breakdowns
- `GET /api/test-responses/:test_id` - Includes detailed subject performance

### Response Format Example
```json
{
  "student_id": 123,
  "test_id": 456,
  "total_score": 15,
  "total_questions": 20,
  "attempted_questions": 18,
  "unattempted_questions": 2,
  "correct_answers": 8,
  "incorrect_answers": 10,
  "subject_breakdown": {
    "Mathematics": {
      "positive_marks": 12,
      "negative_marks": 3,
      "total_questions": 10,
      "attempted_questions": 9,
      "unattempted_questions": 1,
      "correct_answers": 4,
      "incorrect_answers": 5,
      "topic_breakdown": {
        "Algebra": {
          "positive_marks": 8,
          "negative_marks": 1,
          "total_questions": 5,
          "attempted_questions": 4,
          "unattempted_questions": 1,
          "correct_answers": 3,
          "incorrect_answers": 1
        }
      }
    }
  }
}
```

## Backward Compatibility

### ✅ Fully Compatible
- All existing queries continue to work
- New columns have sensible defaults
- Soft migration approach (ADD COLUMN IF NOT EXISTS)
- No breaking changes to existing APIs

### 🔄 Enhanced Functionality
- Existing test evaluation now includes subject-wise scoring
- Rankings are more detailed and performant
- Historical data remains intact

## Testing

### Automated Tests
- **`TestRecordTestResponses`** - Verifies subject-wise scoring functionality
- **Integration tests** - Ensure API compatibility
- **Performance tests** - Validate ranking query speed

### Manual Verification
```sql
-- Check schema structure
\d student_test_marks

-- Verify indexes
\di student_test_marks*

-- Test JSONB queries
SELECT subject_marks->'Mathematics' FROM student_test_marks LIMIT 1;
```

## Rollback Plan

If needed, the changes can be rolled back:

```sql
-- Remove new columns (will lose subject-wise data)
ALTER TABLE student_test_marks DROP COLUMN IF EXISTS subject_marks;
ALTER TABLE test_responses DROP COLUMN IF EXISTS calculated_score;
ALTER TABLE test_responses DROP COLUMN IF EXISTS is_correct;

-- Remove new indexes
DROP INDEX IF EXISTS idx_student_test_marks_subject_marks_gin;
DROP INDEX IF EXISTS idx_student_test_marks_student_test;
-- ... other new indexes
```

## Support

For questions or issues:
1. Check the detailed documentation in `docs/subject_wise_scoring_schema.md`
2. Run the verification script to diagnose issues
3. Review the test cases in `cmd/server/http/test/test_apis_test.go`

---

**Status**: ✅ Ready for Production  
**Last Updated**: 2025-09-09  
**Version**: 1.0.0
