package test

import (
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"
)

func BenchmarkGetTestAnswerKey_Student(b *testing.B) {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	studentEmail := "bench_student_" + timestamp + "@example.com"
	testName := "Benchmark Test " + timestamp

	// Setup test data (similar to main test but simplified)
	setupBenchmarkData(b, studentEmail, testName)

	// Get student token
	studentToken := createBenchmarkStudent(b, studentEmail, timestamp)

	// Get test ID
	var test models.Test
	db.Where("name = ?", testName).First(&test)

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			url := fmt.Sprintf("/api/tests/%d/answer-key", test.ID)
			resp := authenticatedRequestHelper(http.MethodGet, url, nil, studentToken)
			if resp.Code != http.StatusOK {
				b<PERSON>("Expected status 200, got %d", resp.Code)
			}
		}
	})
}

func BenchmarkGetTestAnswerKey_Admin(b *testing.B) {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	adminEmail := "bench_admin_" + timestamp + "@example.com"
	testName := "Benchmark Admin Test " + timestamp

	// Setup test data
	setupBenchmarkData(b, "", testName)

	// Get admin token
	adminToken := createBenchmarkAdmin(b, adminEmail, timestamp)

	// Get test ID
	var test models.Test
	db.Where("name = ?", testName).First(&test)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			url := fmt.Sprintf("/api/tests/%d/answer-key", test.ID)
			resp := authenticatedRequestHelper(http.MethodGet, url, nil, adminToken)
			if resp.Code != http.StatusOK {
				b.Errorf("Expected status 200, got %d", resp.Code)
			}
		}
	})
}

func setupBenchmarkData(b *testing.B, studentEmail, testName string) {
	// Create minimal test setup for benchmarking
	subject := models.Subject{Name: "Benchmark Subject", DisplayName: "Benchmark Subject"}
	db.Create(&subject)

	chapter := models.Chapter{Name: "Benchmark Chapter", SubjectID: subject.ID}
	db.Create(&chapter)

	topic := models.Topic{Name: "Benchmark Topic", ChapterID: chapter.ID}
	db.Create(&topic)

	difficulty := models.Difficulty{Name: "Benchmark Difficulty"}
	db.Create(&difficulty)

	question := models.Question{
		Text:         "Benchmark question?",
		TopicID:      topic.ID,
		DifficultyID: difficulty.ID,
		QuestionType: "mcq",
	}
	db.Create(&question)

	testType := models.TestType{Name: "Benchmark Test Type"}
	db.Create(&testType)

	test := models.Test{
		Name:       testName,
		TestTypeID: testType.ID,
		FromTime:   time.Now(),
		ToTime:     time.Now().Add(time.Hour),
		Active:     true,
	}
	db.Create(&test)

	sectionType := models.SectionType{Name: "Benchmark Section Type", SubjectID: subject.ID}
	db.Create(&sectionType)

	section := models.Section{
		Name:          "Benchmark Section",
		TestID:        test.ID,
		SectionTypeID: sectionType.ID,
	}
	db.Create(&section)

	err := db.Model(&section).Association("Questions").Append(&question)
	if err != nil {
		b.Fatalf("Failed to append question to section: %v", err)
	}
}

func createBenchmarkStudent(b *testing.B, email, timestamp string) string {
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Benchmark Student",
			Email:          email,
			PhoneNumber:    "1111111111",
			ContactAddress: "Benchmark Street",
		},
		ParentPhone: "2222222222",
		ParentEmail: "benchmark_parent_" + timestamp + "@example.com",
		Password:    "benchmarkpassword123",
	}

	resp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	if resp.Code != http.StatusOK {
		b.Fatalf("Failed to create benchmark student: %d", resp.Code)
	}

	// Return a mock token for benchmarking
	return "benchmark-student-token"
}

func createBenchmarkAdmin(b *testing.B, email, timestamp string) string {
	adminPayload := models.AdminForCreate{
		FullName:       "Benchmark Admin",
		Email:          email,
		PhoneNumber:    "3333333333",
		ContactAddress: "Benchmark Admin Street",
		Password:       "benchmarkadminpassword123",
	}

	resp := requestExecutionHelper(http.MethodPost, "/api/admins", adminPayload)
	if resp.Code != http.StatusCreated {
		b.Fatalf("Failed to create benchmark admin: %d", resp.Code)
	}

	// Return a mock token for benchmarking
	return "benchmark-admin-token"
}
