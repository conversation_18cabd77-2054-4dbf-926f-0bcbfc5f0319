-- Migration to enhance database schema for comprehensive subject-wise scoring functionality
-- This migration ensures all tables and indexes are properly configured for subject-wise scoring and ranking
-- Run this script to update existing database schema for enhanced test evaluation

-- ============================================================================
-- 1. ENSURE STUDENT_TEST_MARKS TABLE HAS ALL REQUIRED COLUMNS AND CONSTRAINTS
-- ============================================================================

-- Add subject_marks column if it doesn't exist (should already exist from previous migration)
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS subject_marks JSONB;

-- Add question statistics columns
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS total_questions INTEGER DEFAULT 0 NOT NULL;
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS attempted_questions INTEGER DEFAULT 0 NOT NULL;
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS unattempted_questions INTEGER DEFAULT 0 NOT NULL;
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS correct_answers INTEGER DEFAULT 0 NOT NULL;
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS incorrect_answers INTEGER DEFAULT 0 NOT NULL;

-- Ensure the generated column for final_marks is properly configured
-- Note: This may require dropping and recreating if the column already exists with different definition
DO $$
BEGIN
    -- Check if final_marks column exists and is a generated column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'student_test_marks' 
        AND column_name = 'final_marks' 
        AND is_generated = 'ALWAYS'
    ) THEN
        -- If final_marks exists but is not generated, drop it first
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'student_test_marks' 
            AND column_name = 'final_marks'
        ) THEN
            ALTER TABLE student_test_marks DROP COLUMN final_marks;
        END IF;
        
        -- Add the generated column
        ALTER TABLE student_test_marks ADD COLUMN final_marks INTEGER GENERATED ALWAYS AS (total_positive_marks - total_negative_marks) STORED NOT NULL;
    END IF;
END $$;

-- ============================================================================
-- 2. OPTIMIZE INDEXES FOR SUBJECT-WISE SCORING AND RANKING QUERIES
-- ============================================================================

-- Primary index for test-specific rankings (test_id, final_marks DESC)
-- This enables fast queries like: SELECT * FROM student_test_marks WHERE test_id = ? ORDER BY final_marks DESC
CREATE INDEX IF NOT EXISTS idx_test_final_marks_desc ON student_test_marks (test_id, final_marks DESC);

-- GIN index on subject_marks JSONB column for efficient subject-wise queries
CREATE INDEX IF NOT EXISTS idx_student_test_marks_subject_marks_gin ON student_test_marks USING GIN (subject_marks);

-- Additional index for deleted_at to support soft deletes efficiently
CREATE INDEX IF NOT EXISTS idx_student_test_marks_deleted_at ON student_test_marks (deleted_at) WHERE deleted_at IS NOT NULL;

-- Composite index for student-specific test history queries
CREATE INDEX IF NOT EXISTS idx_student_test_marks_student_test ON student_test_marks (student_id, test_id);

-- ============================================================================
-- 3. ENSURE TEST_RESPONSES TABLE IS OPTIMIZED FOR EVALUATION QUERIES
-- ============================================================================

-- Ensure calculated_score and is_correct columns exist (should already exist)
ALTER TABLE test_responses ADD COLUMN IF NOT EXISTS calculated_score INTEGER;
ALTER TABLE test_responses ADD COLUMN IF NOT EXISTS is_correct BOOLEAN DEFAULT FALSE;

-- Optimize indexes for test response evaluation queries
CREATE INDEX IF NOT EXISTS idx_test_responses_student_test ON test_responses (student_id, test_id);
CREATE INDEX IF NOT EXISTS idx_test_responses_test_question ON test_responses (test_id, question_id);

-- ============================================================================
-- 4. ENSURE SECTION TYPES TABLE SUPPORTS FLEXIBLE SCORING CONFIGURATION
-- ============================================================================

-- Ensure positive_marks and negative_marks are NUMERIC for flexible scoring
-- (Should already be correct from the base schema)
DO $$
BEGIN
    -- Check if columns are already NUMERIC type
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'section_types' 
        AND column_name = 'positive_marks' 
        AND data_type = 'numeric'
    ) THEN
        ALTER TABLE section_types ALTER COLUMN positive_marks TYPE NUMERIC;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'section_types' 
        AND column_name = 'negative_marks' 
        AND data_type = 'numeric'
    ) THEN
        ALTER TABLE section_types ALTER COLUMN negative_marks TYPE NUMERIC;
    END IF;
END $$;

-- ============================================================================
-- 5. ADD HELPFUL VIEWS FOR SUBJECT-WISE ANALYSIS (OPTIONAL)
-- ============================================================================

-- Create a view for easy subject-wise ranking analysis
CREATE OR REPLACE VIEW subject_wise_rankings AS
SELECT 
    stm.student_id,
    stm.test_id,
    stm.final_marks,
    stm.total_positive_marks,
    stm.total_negative_marks,
    stm.subject_marks,
    s.user_id,
    u.full_name as student_name,
    u.email as student_email,
    t.name as test_name,
    ROW_NUMBER() OVER (PARTITION BY stm.test_id ORDER BY stm.final_marks DESC) as rank,
    PERCENT_RANK() OVER (PARTITION BY stm.test_id ORDER BY stm.final_marks DESC) as percentile
FROM student_test_marks stm
JOIN students s ON stm.student_id = s.id
JOIN users u ON s.user_id = u.id
JOIN tests t ON stm.test_id = t.id
WHERE stm.deleted_at IS NULL
  AND s.deleted_at IS NULL
  AND u.deleted_at IS NULL
  AND t.deleted_at IS NULL;

-- ============================================================================
-- 6. VERIFICATION QUERIES
-- ============================================================================

-- Verify the schema updates
SELECT 'Schema verification completed. Checking key components...' as status;

-- Check student_test_marks table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    is_generated
FROM information_schema.columns
WHERE table_name = 'student_test_marks' 
AND column_name IN ('subject_marks', 'final_marks', 'total_positive_marks', 'total_negative_marks')
ORDER BY column_name;

-- Check indexes on student_test_marks
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'student_test_marks' 
AND indexname LIKE '%subject_marks%' OR indexname LIKE '%final_marks%'
ORDER BY indexname;

-- Check section_types scoring columns
SELECT 
    column_name, 
    data_type, 
    numeric_precision,
    numeric_scale
FROM information_schema.columns
WHERE table_name = 'section_types' 
AND column_name IN ('positive_marks', 'negative_marks')
ORDER BY column_name;

SELECT 'Subject-wise scoring schema enhancement completed successfully!' as result;
