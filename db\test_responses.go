package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// RecordTestResponses records all responses for a test from a student
func (p *DbPlugin) RecordTestResponses(ctx context.Context, studentID uint, testResponsesInput *models.TestResponsesForCreate) (*models.TestResponsesResult, error) {
	start := time.Now()

	slog.Info("Recording test responses",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"response_count", len(testResponsesInput.Responses),
	)

	// Verify the test exists and is active (read-only operation, no transaction needed)
	var test models.Test
	if err := p.db.Preload("Sections.Questions.Options").Preload("Sections.SectionType").First(&test, testResponsesInput.TestID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.<PERSON>rror(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testResponsesInput.TestID, err)
	}

	if !test.Active {
		duration := time.Since(start)
		slog.Warn("Attempt to submit responses to inactive test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d is not active", testResponsesInput.TestID)
	}

	// Verify the student exists (read-only operation, no transaction needed)
	var student models.Student
	if err := p.db.First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Student not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student with ID %d not found: %w", studentID, err)
	}

	// Check if student has already submitted responses for this test (read-only operation)
	var existingResponseCount int64
	if err := p.db.Model(&models.TestResponse{}).
		Where("student_id = ? AND test_id = ?", studentID, testResponsesInput.TestID).
		Count(&existingResponseCount).Error; err != nil {
		return nil, fmt.Errorf("failed to check existing responses: %w", err)
	}

	if existingResponseCount > 0 {
		duration := time.Since(start)
		slog.Warn("Student already submitted responses for this test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"existing_count", existingResponseCount,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student has already submitted responses for test ID %d", testResponsesInput.TestID)
	}

	// Create a map of question ID to question for quick lookup
	questionMap := make(map[uint]models.Question)
	// Create a map of question ID to section for scoring lookup
	questionToSectionMap := make(map[uint]models.Section)
	for _, section := range test.Sections {
		for _, question := range section.Questions {
			questionMap[question.ID] = question
			questionToSectionMap[question.ID] = section
		}
	}

	// Validate that all questions in the responses belong to this test
	for _, responseInput := range testResponsesInput.Responses {
		if _, exists := questionMap[responseInput.QuestionID]; !exists {
			return nil, fmt.Errorf("question with ID %d is not part of test %d", responseInput.QuestionID, testResponsesInput.TestID)
		}
	}

	// Now start transaction for actual write operations
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Process each response with immediate evaluation
	var responseResults []models.TestResponseResult
	totalScore := 0
	correctAnswers := 0

	// Track evaluations for subject-wise scoring
	evaluatedResponses := make(map[uint]struct {
		isCorrect bool
		score     int
	})

	for _, responseInput := range testResponsesInput.Responses {
		// Get the question for evaluation
		question, exists := questionMap[responseInput.QuestionID]
		if !exists {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Question not found in test during response recording",
				"student_id", studentID,
				"test_id", testResponsesInput.TestID,
				"question_id", responseInput.QuestionID,
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("question %d not found in test %d", responseInput.QuestionID, testResponsesInput.TestID)
		}

		// Create the test response record
		testResponse := models.TestResponse{
			StudentID:         studentID,
			TestID:            testResponsesInput.TestID,
			QuestionID:        responseInput.QuestionID,
			SelectedOptionIDs: responseInput.SelectedOptionIDs,
			ResponseText:      responseInput.ResponseText,
		}

		// Get the section for this question to use proper scoring
		section := questionToSectionMap[responseInput.QuestionID]

		// Evaluate the response immediately using section type scoring
		isCorrect, calculatedScore := p.evaluateResponseWithSectionScoring(&question, &testResponse, section.SectionType.PositiveMarks, section.SectionType.NegativeMarks)
		testResponse.IsCorrect = isCorrect
		testResponse.CalculatedScore = &calculatedScore

		// Store evaluation for subject-wise scoring
		evaluatedResponses[responseInput.QuestionID] = struct {
			isCorrect bool
			score     int
		}{
			isCorrect: isCorrect,
			score:     calculatedScore,
		}

		// Update totals
		totalScore += calculatedScore
		if isCorrect {
			correctAnswers++
		}

		// Update question attempt counts based on evaluation
		if err := p.updateQuestionAttemptCounts(tx, responseInput.QuestionID, isCorrect); err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to update question attempt counts",
				"student_id", studentID,
				"test_id", testResponsesInput.TestID,
				"question_id", responseInput.QuestionID,
				"is_correct", isCorrect,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to update attempt counts for question %d: %w", responseInput.QuestionID, err)
		}

		// Save the response with evaluation results
		if err := tx.Create(&testResponse).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to save test response",
				"student_id", studentID,
				"test_id", testResponsesInput.TestID,
				"question_id", responseInput.QuestionID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to save response for question %d: %w", responseInput.QuestionID, err)
		}

		// Add to results with evaluation data
		responseResult := models.TestResponseResult{
			QuestionID:      responseInput.QuestionID,
			IsCorrect:       isCorrect,
			CalculatedScore: &calculatedScore,
			Message:         "Response recorded and evaluated successfully",
		}

		responseResults = append(responseResults, responseResult)

		slog.Debug("Response evaluated during recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"question_id", responseInput.QuestionID,
			"is_correct", isCorrect,
			"calculated_score", calculatedScore,
		)
	}

	// Calculate subject-wise and topic-wise scores with question statistics
	subjectMarks, totalPositiveMarks, totalNegativeMarks, totalQuestions, attemptedQuestions, unattemptedQuestions, correctAnswers, incorrectAnswers, err := p.calculateSubjectWiseScores(testResponsesInput.TestID, testResponsesInput.Responses, evaluatedResponses)
	if err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to calculate subject-wise scores",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to calculate subject-wise scores: %w", err)
	}

	// Create or update StudentTestMark record with subject-wise scores and question statistics
	if err := p.createOrUpdateStudentTestMark(tx, studentID, testResponsesInput.TestID, subjectMarks, totalPositiveMarks, totalNegativeMarks, totalQuestions, attemptedQuestions, unattemptedQuestions, correctAnswers, incorrectAnswers); err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to create/update StudentTestMark",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to create/update StudentTestMark: %w", err)
	}

	slog.Info("Subject-wise scores calculated and stored",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"total_positive_marks", totalPositiveMarks,
		"total_negative_marks", totalNegativeMarks,
		"subjects_count", len(subjectMarks),
	)

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit test responses transaction",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit responses: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Test responses recorded and evaluated successfully",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"total_questions", len(testResponsesInput.Responses),
		"correct_answers", correctAnswers,
		"total_score", totalScore,
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result with evaluation data
	result := &models.TestResponsesResult{
		TestID:          testResponsesInput.TestID,
		StudentID:       studentID,
		TotalQuestions:  len(testResponsesInput.Responses),
		CorrectAnswers:  correctAnswers,
		TotalScore:      totalScore,
		ResponseResults: responseResults,
		Message:         fmt.Sprintf("Successfully recorded and evaluated %d responses. Score: %d/%d correct answers.", len(testResponsesInput.Responses), correctAnswers, len(testResponsesInput.Responses)),
	}

	return result, nil
}

// GetStudentTestResult retrieves test results for a specific student including scores, question statistics, and rankings
func (p *DbPlugin) GetStudentTestResult(ctx context.Context, studentID, testID uint) (*models.StudentTestResult, error) {
	start := time.Now()

	slog.Debug("Getting student test results",
		"student_id", studentID,
		"test_id", testID,
	)

	// Get test details (read-only operation)
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for student results",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get student details (read-only operation)
	var student models.Student
	if err := p.db.Preload("User").First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Student not found for results",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student with ID %d not found: %w", studentID, err)
	}

	// Get student test marks (contains all the score and statistics data)
	var studentTestMark models.StudentTestMark
	if err := p.db.Where("student_id = ? AND test_id = ?", studentID, testID).First(&studentTestMark).Error; err != nil {
		duration := time.Since(start)
		slog.Info("No test results found for student",
			"student_id", studentID,
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.StudentTestResult{
			TestID:            testID,
			TestName:          test.Name,
			StudentID:         studentID,
			StudentName:       student.User.FullName,
			OverallScore:      0,
			OverallQuestions:  models.QuestionStatistics{},
			SubjectWiseScores: make(map[string]models.SubjectScore),
			OverallRank:       nil,
			SubjectWiseRanks:  make(map[string]models.SubjectRankInfo),
			Message:           "No test results found for this student",
		}, nil
	}

	// Build overall question statistics
	overallQuestions := models.QuestionStatistics{
		Total:       studentTestMark.TotalQuestions,
		Attempted:   studentTestMark.AttemptedQuestions,
		Unattempted: studentTestMark.UnattemptedQuestions,
		Correct:     studentTestMark.CorrectAnswers,
		Incorrect:   studentTestMark.IncorrectAnswers,
	}

	// Build subject-wise scores
	subjectWiseScores := make(map[string]models.SubjectScore)
	for subjectName, subjectMarks := range studentTestMark.SubjectMarks {
		subjectWiseScores[subjectName] = models.SubjectScore{
			PositiveMarks: subjectMarks.PositiveMarks,
			NegativeMarks: subjectMarks.NegativeMarks,
			FinalMarks:    subjectMarks.PositiveMarks - subjectMarks.NegativeMarks,
			Questions: models.QuestionStatistics{
				Total:       subjectMarks.TotalQuestions,
				Attempted:   subjectMarks.AttemptedQuestions,
				Unattempted: subjectMarks.UnattemptedQuestions,
				Correct:     subjectMarks.CorrectAnswers,
				Incorrect:   subjectMarks.IncorrectAnswers,
			},
		}
	}

	// Get ranking information by calling GetTestRankings and finding this student
	var overallRank *int
	var subjectWiseRanks = make(map[string]models.SubjectRankInfo)

	// Get all rankings for this test to find the student's position
	rankingResult, err := p.GetTestRankings(ctx, testID, 0, 0) // Get all rankings
	if err == nil && rankingResult != nil {
		// Find this student's overall rank
		for _, ranking := range rankingResult.StudentRankings {
			if ranking.StudentID == studentID {
				overallRank = &ranking.Rank
				subjectWiseRanks = ranking.SubjectRanks
				break
			}
		}
	}

	duration := time.Since(start)
	slog.Debug("Student test results retrieved successfully",
		"student_id", studentID,
		"test_id", testID,
		"overall_score", studentTestMark.FinalMarks,
		"overall_rank", overallRank,
		"subject_count", len(subjectWiseScores),
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.StudentTestResult{
		TestID:            testID,
		TestName:          test.Name,
		StudentID:         studentID,
		StudentName:       student.User.FullName,
		OverallScore:      studentTestMark.FinalMarks,
		OverallQuestions:  overallQuestions,
		SubjectWiseScores: subjectWiseScores,
		OverallRank:       overallRank,
		SubjectWiseRanks:  subjectWiseRanks,
		Message:           fmt.Sprintf("Retrieved test results with overall score: %d", studentTestMark.FinalMarks),
	}

	return result, nil
}

// GetTestRankings retrieves rankings for all students in a specific test
func (p *DbPlugin) GetTestRankings(ctx context.Context, testID uint, limit, offset int) (*models.TestRankingResult, error) {
	start := time.Now()

	slog.Info("Getting test rankings",
		"test_id", testID,
		"limit", limit,
		"offset", offset,
	)

	// Verify the test exists and get test details (read-only operation)
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get all student marks for this test that are eligible for ranking, ordered by final marks descending
	var studentMarks []models.StudentTestMark
	query := p.db.Preload("Student.User").
		Where("test_id = ? AND eligible_for_ranking = ?", testID, true).
		Order("final_marks DESC, student_id ASC") // Secondary sort by student_id for consistent ordering

	// Apply pagination if specified
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&studentMarks).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student marks for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve student marks: %w", err)
	}

	if len(studentMarks) == 0 {
		duration := time.Since(start)
		slog.Info("No student marks found for test",
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.TestRankingResult{
			TestID:          testID,
			TestName:        test.Name,
			TotalStudents:   0,
			SubjectScores:   make(map[string]models.SubjectScoreInfo),
			StudentRankings: []models.StudentRankingInfo{},
			Message:         "No student marks found for this test",
		}, nil
	}

	// Calculate statistics
	var totalMarks int
	highestMarks := studentMarks[0].FinalMarks
	lowestMarks := studentMarks[len(studentMarks)-1].FinalMarks

	for _, mark := range studentMarks {
		totalMarks += mark.FinalMarks
		if mark.FinalMarks > highestMarks {
			highestMarks = mark.FinalMarks
		}
		if mark.FinalMarks < lowestMarks {
			lowestMarks = mark.FinalMarks
		}
	}

	averageMarks := float64(totalMarks) / float64(len(studentMarks))

	// Get total count for percentile calculation (if pagination is used) - only eligible students
	var totalStudents int64
	if err := p.db.Model(&models.StudentTestMark{}).
		Where("test_id = ? AND eligible_for_ranking = ?", testID, true).
		Count(&totalStudents).Error; err != nil {
		return nil, fmt.Errorf("failed to count total students: %w", err)
	}

	// Calculate subject-wise and topic-wise average scores from stored marks
	subjectScores := p.calculateSubjectScoresFromMarks(studentMarks)

	// Calculate subject-wise rankings for all students
	subjectRankings := p.calculateSubjectWiseRankings(studentMarks)

	// Build student rankings with rank and percentile
	var studentRankings []models.StudentRankingInfo
	currentRank := offset + 1 // Start rank based on offset

	for i, mark := range studentMarks {
		// Handle tied scores - students with same marks get same rank
		if i > 0 && mark.FinalMarks < studentMarks[i-1].FinalMarks {
			currentRank = offset + i + 1
		}

		// Calculate percentile (percentage of students with lower scores)
		percentile := float64(int(totalStudents)-currentRank+1) / float64(totalStudents) * 100

		// Find subject-wise ranks for this student
		subjectRanks := make(map[string]models.SubjectRankInfo)
		for subjectName, rankings := range subjectRankings {
			for _, ranking := range rankings {
				// Match by student ID
				if ranking.StudentID == uint(mark.StudentID) {
					subjectRanks[subjectName] = ranking
					break
				}
			}
		}

		studentRanking := models.StudentRankingInfo{
			StudentID:          uint(mark.StudentID),
			StudentName:        mark.Student.User.FullName,
			StudentEmail:       mark.Student.User.Email,
			TotalPositiveMarks: mark.TotalPositiveMarks,
			TotalNegativeMarks: mark.TotalNegativeMarks,
			FinalMarks:         mark.FinalMarks,
			Rank:               currentRank,
			Percentile:         percentile,
			SubjectRanks:       subjectRanks,
		}

		studentRankings = append(studentRankings, studentRanking)
	}

	duration := time.Since(start)
	slog.Info("Test rankings retrieved successfully",
		"test_id", testID,
		"total_students", totalStudents,
		"returned_count", len(studentRankings),
		"highest_marks", highestMarks,
		"lowest_marks", lowestMarks,
		"average_marks", averageMarks,
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result
	result := &models.TestRankingResult{
		TestID:          testID,
		TestName:        test.Name,
		TotalStudents:   int(totalStudents),
		HighestMarks:    highestMarks,
		LowestMarks:     lowestMarks,
		AverageMarks:    averageMarks,
		SubjectScores:   subjectScores,
		StudentRankings: studentRankings,
		Message:         fmt.Sprintf("Retrieved rankings for %d students (showing %d)", totalStudents, len(studentRankings)),
	}

	return result, nil
}

// evaluateResponseWithSectionScoring evaluates a response using section type scoring rules
func (p *DbPlugin) evaluateResponseWithSectionScoring(question *models.Question, response *models.TestResponse, positiveMarks, negativeMarks float64) (bool, int) {
	// For multiple choice questions, check selected options
	if len(response.SelectedOptionIDs) > 0 {
		isCorrect := p.isMultipleChoiceResponseCorrect(question, response)
		if isCorrect {
			return true, int(positiveMarks)
		}
		return false, -int(negativeMarks) // Return negative score for incorrect answers
	}

	// For text-based questions, compare with correct answer
	if response.ResponseText != nil {
		isCorrect := p.isTextResponseCorrect(question, response)
		if isCorrect {
			return true, int(positiveMarks)
		}
		return false, -int(negativeMarks) // Return negative score for incorrect answers
	}

	// No response provided - no penalty
	return false, 0
}

// isMultipleChoiceResponseCorrect checks if a multiple choice response is correct (without scoring)
func (p *DbPlugin) isMultipleChoiceResponseCorrect(question *models.Question, response *models.TestResponse) bool {
	// Load question options if not already loaded
	if len(question.Options) == 0 {
		if err := p.db.Where("question_id = ?", question.ID).Find(&question.Options).Error; err != nil {
			slog.Error("Failed to load question options for evaluation",
				"question_id", question.ID,
				"error", err.Error(),
			)
			return false
		}
	}

	// Get correct option IDs
	var correctOptionIDs []int
	for _, option := range question.Options {
		if option.IsCorrect {
			correctOptionIDs = append(correctOptionIDs, int(option.ID))
		}
	}

	// Compare selected options with correct options
	if len(correctOptionIDs) == 0 {
		slog.Warn("No correct options found for question", "question_id", question.ID)
		return false
	}

	// Check if selected options match correct options exactly
	if len(response.SelectedOptionIDs) != len(correctOptionIDs) {
		return false
	}

	// Create maps for comparison
	selectedMap := make(map[int]bool)
	for _, id := range response.SelectedOptionIDs {
		selectedMap[id] = true
	}

	correctMap := make(map[int]bool)
	for _, id := range correctOptionIDs {
		correctMap[id] = true
	}

	// Check if all selected options are correct and all correct options are selected
	for _, id := range response.SelectedOptionIDs {
		if !correctMap[id] {
			return false
		}
	}

	return true
}

// isTextResponseCorrect checks if a text response is correct (without scoring)
func (p *DbPlugin) isTextResponseCorrect(question *models.Question, response *models.TestResponse) bool {
	if response.ResponseText == nil || question.CorrectAnswer == "" {
		return false
	}

	// Simple string comparison (case-insensitive)
	responseText := *response.ResponseText
	if len(responseText) == 0 {
		return false
	}

	// For now, do exact match (case-insensitive)
	return responseText == question.CorrectAnswer
}

// calculateSubjectScoresFromMarks calculates average scores from stored StudentTestMark records
func (p *DbPlugin) calculateSubjectScoresFromMarks(studentMarks []models.StudentTestMark) map[string]models.SubjectScoreInfo {
	result := make(map[string]models.SubjectScoreInfo)

	if len(studentMarks) == 0 {
		return result
	}

	// Aggregate marks by subject and topic across all students
	subjectTotals := make(map[string]struct {
		totalScore  int
		count       int
		topicTotals map[string]struct {
			totalScore int
			count      int
		}
	})

	for _, mark := range studentMarks {
		if mark.SubjectMarks == nil {
			continue
		}

		for subjectName, subjectMark := range mark.SubjectMarks {
			// Initialize subject if not exists
			if _, exists := subjectTotals[subjectName]; !exists {
				subjectTotals[subjectName] = struct {
					totalScore  int
					count       int
					topicTotals map[string]struct {
						totalScore int
						count      int
					}
				}{
					totalScore: 0,
					count:      0,
					topicTotals: make(map[string]struct {
						totalScore int
						count      int
					}),
				}
			}

			subjectTotal := subjectTotals[subjectName]
			subjectScore := subjectMark.PositiveMarks - subjectMark.NegativeMarks
			subjectTotal.totalScore += subjectScore
			subjectTotal.count++

			// Process topic marks
			for topicName, topicMark := range subjectMark.TopicMarks {
				if _, exists := subjectTotal.topicTotals[topicName]; !exists {
					subjectTotal.topicTotals[topicName] = struct {
						totalScore int
						count      int
					}{totalScore: 0, count: 0}
				}

				topicTotal := subjectTotal.topicTotals[topicName]
				topicScore := topicMark.PositiveMarks - topicMark.NegativeMarks
				topicTotal.totalScore += topicScore
				topicTotal.count++
				subjectTotal.topicTotals[topicName] = topicTotal
			}

			subjectTotals[subjectName] = subjectTotal
		}
	}

	// Calculate averages
	for subjectName, subjectTotal := range subjectTotals {
		if subjectTotal.count == 0 {
			continue
		}

		subjectAverage := float64(subjectTotal.totalScore) / float64(subjectTotal.count)
		topicAverages := make(map[string]float64)

		for topicName, topicTotal := range subjectTotal.topicTotals {
			if topicTotal.count > 0 {
				topicAverages[topicName] = float64(topicTotal.totalScore) / float64(topicTotal.count)
			}
		}

		result[subjectName] = models.SubjectScoreInfo{
			AverageScore: subjectAverage,
			TopicScores:  topicAverages,
		}
	}

	return result
}

// calculateSubjectWiseScores calculates subject-wise and topic-wise scores from test responses with question statistics
func (p *DbPlugin) calculateSubjectWiseScores(testID uint, responses []models.TestResponseForCreate, evaluatedResponses map[uint]struct {
	isCorrect bool
	score     int
}) (models.SubjectMarksMap, int, int, int, int, int, int, int, error) {
	// Load test with sections, questions, and their topic/subject relationships
	var test models.Test
	if err := p.db.Preload("Sections.SectionType").
		Preload("Sections.Questions.Topic.Chapter.Subject").
		Where("id = ?", testID).
		First(&test).Error; err != nil {
		return nil, 0, 0, 0, 0, 0, 0, 0, fmt.Errorf("failed to load test with subject information: %w", err)
	}

	// Create maps for quick lookup
	questionToSubject := make(map[uint]string)
	questionToTopic := make(map[uint]string)
	sectionTypeMarks := make(map[uint]struct {
		positive float64
		negative float64
	})

	// Track all questions in the test by subject and topic
	subjectQuestionCounts := make(map[string]int)
	topicQuestionCounts := make(map[string]int)
	allTestQuestions := make(map[uint]bool) // Track all questions in the test

	// Build lookup maps and count questions
	for _, section := range test.Sections {
		sectionTypeMarks[section.SectionTypeID] = struct {
			positive float64
			negative float64
		}{
			positive: section.SectionType.PositiveMarks,
			negative: section.SectionType.NegativeMarks,
		}

		for _, question := range section.Questions {
			allTestQuestions[question.ID] = true
			if question.Topic.Chapter.Subject.Name != "" {
				subjectName := question.Topic.Chapter.Subject.Name
				topicName := question.Topic.Name

				questionToSubject[question.ID] = subjectName
				questionToTopic[question.ID] = topicName

				// Count questions per subject and topic
				subjectQuestionCounts[subjectName]++
				topicQuestionCounts[topicName]++
			}
		}
	}

	// Initialize subject marks with question counts
	subjectMarks := make(models.SubjectMarksMap)
	for subjectName, questionCount := range subjectQuestionCounts {
		subjectMarks[subjectName] = models.SubjectMarks{
			PositiveMarks:        0,
			NegativeMarks:        0,
			TotalQuestions:       questionCount,
			AttemptedQuestions:   0,
			UnattemptedQuestions: questionCount, // Initially all are unattempted
			CorrectAnswers:       0,
			IncorrectAnswers:     0,
			TopicMarks:           make(map[string]models.TopicMarks),
		}
	}

	// Initialize topic marks with question counts
	for topicName, questionCount := range topicQuestionCounts {
		// Find which subject this topic belongs to
		var subjectName string
		for _, section := range test.Sections {
			for _, question := range section.Questions {
				if question.Topic.Name == topicName {
					subjectName = question.Topic.Chapter.Subject.Name
					break
				}
			}
			if subjectName != "" {
				break
			}
		}

		if subjectName != "" {
			subjectMark := subjectMarks[subjectName]
			subjectMark.TopicMarks[topicName] = models.TopicMarks{
				PositiveMarks:        0,
				NegativeMarks:        0,
				TotalQuestions:       questionCount,
				AttemptedQuestions:   0,
				UnattemptedQuestions: questionCount, // Initially all are unattempted
				CorrectAnswers:       0,
				IncorrectAnswers:     0,
			}
			subjectMarks[subjectName] = subjectMark
		}
	}

	// Track overall statistics
	totalQuestions := len(allTestQuestions)
	attemptedQuestions := len(responses)
	unattemptedQuestions := totalQuestions - attemptedQuestions
	correctAnswers := 0
	incorrectAnswers := 0
	totalPositiveMarks := 0
	totalNegativeMarks := 0

	// Process each response and update statistics
	for _, response := range responses {
		evaluation, exists := evaluatedResponses[response.QuestionID]
		if !exists {
			continue
		}

		subjectName, hasSubject := questionToSubject[response.QuestionID]
		topicName, hasTopic := questionToTopic[response.QuestionID]

		if !hasSubject || !hasTopic {
			continue
		}

		// Get current subject and topic marks
		subjectMark := subjectMarks[subjectName]
		topicMark := subjectMark.TopicMarks[topicName]

		// Update attempted questions count
		subjectMark.AttemptedQuestions++
		subjectMark.UnattemptedQuestions--
		topicMark.AttemptedQuestions++
		topicMark.UnattemptedQuestions--

		// Find the section type marks for this question
		var positiveMarks, negativeMarks float64 = 1, 0 // Default values
		for _, section := range test.Sections {
			for _, question := range section.Questions {
				if question.ID == response.QuestionID {
					if marks, exists := sectionTypeMarks[section.SectionTypeID]; exists {
						positiveMarks = marks.positive
						negativeMarks = marks.negative
					}
					break
				}
			}
		}

		// Update scores and answer statistics based on evaluation
		if evaluation.isCorrect {
			// Correct answer
			scoreToAdd := int(positiveMarks)
			subjectMark.PositiveMarks += scoreToAdd
			subjectMark.CorrectAnswers++
			topicMark.PositiveMarks += scoreToAdd
			topicMark.CorrectAnswers++
			totalPositiveMarks += scoreToAdd
			correctAnswers++
		} else {
			// Incorrect answer
			scoreToAdd := int(negativeMarks)
			subjectMark.NegativeMarks += scoreToAdd
			subjectMark.IncorrectAnswers++
			topicMark.NegativeMarks += scoreToAdd
			topicMark.IncorrectAnswers++
			totalNegativeMarks += scoreToAdd
			incorrectAnswers++
		}

		// Update the maps
		subjectMark.TopicMarks[topicName] = topicMark
		subjectMarks[subjectName] = subjectMark
	}

	return subjectMarks, totalPositiveMarks, totalNegativeMarks, totalQuestions, attemptedQuestions, unattemptedQuestions, correctAnswers, incorrectAnswers, nil
}

// createOrUpdateStudentTestMark creates or updates a StudentTestMark record with subject-wise scores and question statistics
func (p *DbPlugin) createOrUpdateStudentTestMark(tx *gorm.DB, studentID uint, testID uint, subjectMarks models.SubjectMarksMap, totalPositiveMarks, totalNegativeMarks, totalQuestions, attemptedQuestions, unattemptedQuestions, correctAnswers, incorrectAnswers int) error {
	// Calculate eligible_for_ranking based on test timing
	eligibleForRanking := p.isEligibleForRanking(tx, testID)

	studentTestMark := models.StudentTestMark{
		StudentID:            int(studentID),
		TestID:               int(testID),
		TotalPositiveMarks:   totalPositiveMarks,
		TotalNegativeMarks:   totalNegativeMarks,
		TotalQuestions:       totalQuestions,
		AttemptedQuestions:   attemptedQuestions,
		UnattemptedQuestions: unattemptedQuestions,
		CorrectAnswers:       correctAnswers,
		IncorrectAnswers:     incorrectAnswers,
		SubjectMarks:         subjectMarks,
		EligibleForRanking:   eligibleForRanking,
	}

	// Use GORM's Clauses for upsert (ON CONFLICT DO UPDATE)
	if err := tx.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "student_id"}, {Name: "test_id"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"total_positive_marks",
			"total_negative_marks",
			"total_questions",
			"attempted_questions",
			"unattempted_questions",
			"correct_answers",
			"incorrect_answers",
			"subject_marks",
			"eligible_for_ranking",
			"updated_at",
		}),
	}).Create(&studentTestMark).Error; err != nil {
		return fmt.Errorf("failed to create/update StudentTestMark: %w", err)
	}

	return nil
}

// isEligibleForRanking determines if marks can be considered for ranking based on test timing
func (p *DbPlugin) isEligibleForRanking(tx *gorm.DB, testID uint) bool {
	var test models.Test
	if err := tx.Select("from_time", "to_time", "duration").Where("id = ?", testID).First(&test).Error; err != nil {
		// If we can't fetch the test, default to not eligible for ranking
		return false
	}

	// Check if both from_time and to_time are defined
	if test.FromTime.IsZero() || test.ToTime.IsZero() {
		return false
	}

	now := time.Now()

	// Calculate the effective end time: ToTime + Duration (if duration is specified)
	effectiveEndTime := test.ToTime
	if test.Duration != nil && *test.Duration > 0 {
		effectiveEndTime = test.ToTime.Add(time.Duration(*test.Duration) * time.Minute)
	}

	return now.After(test.FromTime) && now.Before(effectiveEndTime)
}

// calculateSubjectWiseRankings calculates subject-wise rankings for all students in a test
func (p *DbPlugin) calculateSubjectWiseRankings(studentMarks []models.StudentTestMark) map[string][]models.SubjectRankInfo {
	subjectRankings := make(map[string][]models.SubjectRankInfo)

	if len(studentMarks) == 0 {
		return subjectRankings
	}

	// Group students by subject and their scores
	subjectStudents := make(map[string][]struct {
		studentID     uint
		studentName   string
		positiveMarks int
		negativeMarks int
		finalMarks    int
	})

	for _, mark := range studentMarks {
		if mark.SubjectMarks == nil {
			continue
		}

		for subjectName, subjectMark := range mark.SubjectMarks {
			finalMarks := subjectMark.PositiveMarks - subjectMark.NegativeMarks

			subjectStudents[subjectName] = append(subjectStudents[subjectName], struct {
				studentID     uint
				studentName   string
				positiveMarks int
				negativeMarks int
				finalMarks    int
			}{
				studentID:     uint(mark.StudentID),
				studentName:   mark.Student.User.FullName,
				positiveMarks: subjectMark.PositiveMarks,
				negativeMarks: subjectMark.NegativeMarks,
				finalMarks:    finalMarks,
			})
		}
	}

	// Calculate rankings for each subject
	for subjectName, students := range subjectStudents {
		// Sort students by final marks (descending)
		for i := 0; i < len(students)-1; i++ {
			for j := i + 1; j < len(students); j++ {
				if students[i].finalMarks < students[j].finalMarks {
					students[i], students[j] = students[j], students[i]
				}
			}
		}

		var rankings []models.SubjectRankInfo
		currentRank := 1
		totalStudents := len(students)

		for i, student := range students {
			// Handle tied scores - students with same marks get same rank
			if i > 0 && student.finalMarks < students[i-1].finalMarks {
				currentRank = i + 1
			}

			// Calculate percentile (percentage of students with lower scores)
			percentile := float64(totalStudents-currentRank+1) / float64(totalStudents) * 100

			ranking := models.SubjectRankInfo{
				StudentID:     student.studentID,
				SubjectName:   subjectName,
				PositiveMarks: student.positiveMarks,
				NegativeMarks: student.negativeMarks,
				FinalMarks:    student.finalMarks,
				Rank:          currentRank,
				Percentile:    percentile,
				TotalStudents: totalStudents,
			}

			rankings = append(rankings, ranking)
		}

		subjectRankings[subjectName] = rankings
	}

	return subjectRankings
}

// GetSubjectRankings retrieves rankings for a specific subject in a test
func (p *DbPlugin) GetSubjectRankings(ctx context.Context, testID uint, subjectName string, limit, offset int) (*models.SubjectRankingResult, error) {
	start := time.Now()

	slog.Info("Getting subject rankings",
		"test_id", testID,
		"subject_name", subjectName,
		"limit", limit,
		"offset", offset,
	)

	// Verify the test exists and get test details
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for subject rankings",
			"test_id", testID,
			"subject_name", subjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get all student marks for this test that are eligible for ranking
	var studentMarks []models.StudentTestMark
	if err := p.db.Preload("Student.User").
		Where("test_id = ? AND eligible_for_ranking = ?", testID, true).
		Find(&studentMarks).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student marks for subject rankings",
			"test_id", testID,
			"subject_name", subjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve student marks: %w", err)
	}

	if len(studentMarks) == 0 {
		duration := time.Since(start)
		slog.Info("No student marks found for test",
			"test_id", testID,
			"subject_name", subjectName,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.SubjectRankingResult{
			TestID:        testID,
			TestName:      test.Name,
			SubjectName:   subjectName,
			TotalStudents: 0,
			Rankings:      []models.SubjectRankInfo{},
			TopicScores:   make(map[string]float64),
			Message:       "No student marks found for this test",
		}, nil
	}

	// Calculate subject-wise rankings
	subjectRankings := p.calculateSubjectWiseRankings(studentMarks)

	// Get rankings for the requested subject
	rankings, exists := subjectRankings[subjectName]
	if !exists {
		return &models.SubjectRankingResult{
			TestID:        testID,
			TestName:      test.Name,
			SubjectName:   subjectName,
			TotalStudents: 0,
			Rankings:      []models.SubjectRankInfo{},
			TopicScores:   make(map[string]float64),
			Message:       fmt.Sprintf("No students found for subject '%s' in this test", subjectName),
		}, nil
	}

	// Apply pagination if specified
	totalStudents := len(rankings)
	if offset > 0 && offset < len(rankings) {
		rankings = rankings[offset:]
	}
	if limit > 0 && limit < len(rankings) {
		rankings = rankings[:limit]
	}

	// Calculate statistics for this subject
	var totalMarks int
	highestMarks := rankings[0].FinalMarks
	lowestMarks := rankings[len(rankings)-1].FinalMarks

	for _, ranking := range rankings {
		totalMarks += ranking.FinalMarks
		if ranking.FinalMarks > highestMarks {
			highestMarks = ranking.FinalMarks
		}
		if ranking.FinalMarks < lowestMarks {
			lowestMarks = ranking.FinalMarks
		}
	}

	averageMarks := float64(totalMarks) / float64(len(rankings))

	// Calculate topic-wise scores for this subject
	topicScores := make(map[string]float64)
	topicTotals := make(map[string]struct {
		totalScore int
		count      int
	})

	for _, mark := range studentMarks {
		if mark.SubjectMarks == nil {
			continue
		}

		subjectMark, exists := mark.SubjectMarks[subjectName]
		if !exists {
			continue
		}

		for topicName, topicMark := range subjectMark.TopicMarks {
			if _, exists := topicTotals[topicName]; !exists {
				topicTotals[topicName] = struct {
					totalScore int
					count      int
				}{totalScore: 0, count: 0}
			}

			topicTotal := topicTotals[topicName]
			topicScore := topicMark.PositiveMarks - topicMark.NegativeMarks
			topicTotal.totalScore += topicScore
			topicTotal.count++
			topicTotals[topicName] = topicTotal
		}
	}

	// Calculate averages for topics
	for topicName, topicTotal := range topicTotals {
		if topicTotal.count > 0 {
			topicScores[topicName] = float64(topicTotal.totalScore) / float64(topicTotal.count)
		}
	}

	duration := time.Since(start)
	slog.Info("Subject rankings retrieved successfully",
		"test_id", testID,
		"subject_name", subjectName,
		"total_students", totalStudents,
		"returned_count", len(rankings),
		"highest_marks", highestMarks,
		"lowest_marks", lowestMarks,
		"average_marks", averageMarks,
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.SubjectRankingResult{
		TestID:        testID,
		TestName:      test.Name,
		SubjectName:   subjectName,
		TotalStudents: totalStudents,
		HighestMarks:  highestMarks,
		LowestMarks:   lowestMarks,
		AverageMarks:  averageMarks,
		Rankings:      rankings,
		TopicScores:   topicScores,
		Message:       fmt.Sprintf("Retrieved rankings for %d students in subject '%s' (showing %d)", totalStudents, subjectName, len(rankings)),
	}

	return result, nil
}

// GetTestAnswerKey returns the answer key for a test based on user role
func (p *DbPlugin) GetTestAnswerKey(ctx context.Context, testID uint, userID uint, userRole string) (interface{}, error) {
	start := time.Now()

	slog.Info("Getting test answer key",
		"test_id", testID,
		"user_id", userID,
		"user_role", userRole,
	)

	// Get test with sections and questions
	var test models.Test
	if err := p.db.Preload("TestType").
		Preload("Sections.SectionType").
		Preload("Sections.Questions.Options").
		Preload("Sections.Questions.Topic.Chapter.Subject").
		Preload("Sections.Questions.Difficulty").
		Where("id = ?", testID).
		First(&test).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to find test for answer key",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test not found: %w", err)
	}

	if userRole == "Student" {
		return p.getStudentAnswerKey(test, userID, start)
	} else {
		return p.getAdminAnswerKey(test, start)
	}
}

func (p *DbPlugin) getStudentAnswerKey(test models.Test, userID uint, start time.Time) (*models.TestAnswerKeyForStudent, error) {
	// Get student info directly using user_id
	var student models.Student
	if err := p.db.Preload("User").Where("user_id = ?", userID).First(&student).Error; err != nil {
		return nil, fmt.Errorf("failed to get student info: %w", err)
	}

	// Get student responses for this test
	var responses []models.TestResponse
	p.db.Where("student_id = ? AND test_id = ?", student.ID, test.ID).Find(&responses)

	// Create response map for quick lookup
	responseMap := make(map[uint]*models.TestResponse)
	for i := range responses {
		responseMap[responses[i].QuestionID] = &responses[i]
	}

	var sections []models.TestAnswerKeySectionStudent
	totalScore := 0
	maxPossibleScore := 0

	for _, section := range test.Sections {
		var questions []models.TestAnswerKeyQuestionStudent

		for _, question := range section.Questions {
			maxPossibleScore += int(section.SectionType.PositiveMarks)

			var studentResponse *models.TestAnswerKeyStudentResponse
			if resp, exists := responseMap[question.ID]; exists {
				studentResponse = &models.TestAnswerKeyStudentResponse{
					SelectedOptionIDs: resp.SelectedOptionIDs,
					ResponseText:      resp.ResponseText,
					IsCorrect:         resp.IsCorrect,
					CalculatedScore:   resp.CalculatedScore,
					WasAttempted:      true,
				}
				if resp.CalculatedScore != nil {
					totalScore += *resp.CalculatedScore
				}
			} else {
				score := 0
				studentResponse = &models.TestAnswerKeyStudentResponse{
					SelectedOptionIDs: []int{},
					ResponseText:      nil,
					IsCorrect:         false,
					CalculatedScore:   &score,
					WasAttempted:      false,
				}
			}

			optionsForGet := make([]models.OptionForGet, len(question.Options))
			for i, option := range question.Options {
				optionsForGet[i] = models.OptionForGet{
					OptionID:       option.ID,
					OptionText:     option.OptionText,
					OptionImageURL: option.OptionImageURL,
					IsCorrect:      option.IsCorrect,
				}
			}

			questionInfo := models.TestAnswerKeyQuestionStudent{
				QuestionID:      question.ID,
				Text:            question.Text,
				QuestionType:    question.QuestionType,
				ImageUrl:        stringToPointer(question.ImageUrl),
				FileUrl:         stringToPointer(question.FileUrl),
				Options:         optionsForGet, // Include correct answers and option IDs
				CorrectAnswer:   stringToPointer(question.CorrectAnswer),
				StudentResponse: studentResponse,
				TopicName:       question.Topic.Name,
				DifficultyName:  question.Difficulty.Name,
				SubjectName:     question.Topic.Chapter.Subject.Name,
			}
			questions = append(questions, questionInfo)
		}

		sectionInfo := models.TestAnswerKeySectionStudent{
			SectionID:   section.ID,
			SectionName: section.Name,
			DisplayName: section.DisplayName,
			Questions:   questions,
		}
		sections = append(sections, sectionInfo)
	}

	duration := time.Since(start)
	slog.Info("Student answer key retrieved successfully",
		"test_id", test.ID,
		"student_id", student.ID,
		"total_score", totalScore,
		"max_possible_score", maxPossibleScore,
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.TestAnswerKeyForStudent{
		TestID:           test.ID,
		TestName:         test.Name,
		StudentID:        student.ID,
		StudentName:      student.User.FullName,
		TotalScore:       totalScore,
		MaxPossibleScore: maxPossibleScore,
		Sections:         sections,
		Message:          fmt.Sprintf("Answer key retrieved for student with score %d/%d", totalScore, maxPossibleScore),
	}

	return result, nil
}

func (p *DbPlugin) getAdminAnswerKey(test models.Test, start time.Time) (*models.TestAnswerKeyForAdmin, error) {
	var sections []models.TestAnswerKeySectionAdmin
	maxPossibleScore := 0

	for _, section := range test.Sections {
		var questions []models.TestAnswerKeyQuestionAdmin

		for _, question := range section.Questions {
			maxPossibleScore += int(section.SectionType.PositiveMarks)

			optionsForGet := make([]models.OptionForGet, len(question.Options))
			for i, option := range question.Options {
				optionsForGet[i] = models.OptionForGet{
					OptionID:       option.ID,
					OptionText:     option.OptionText,
					OptionImageURL: option.OptionImageURL,
					IsCorrect:      option.IsCorrect,
				}
			}

			questionInfo := models.TestAnswerKeyQuestionAdmin{
				QuestionID:     question.ID,
				Text:           question.Text,
				QuestionType:   question.QuestionType,
				ImageUrl:       stringToPointer(question.ImageUrl),
				FileUrl:        stringToPointer(question.FileUrl),
				Options:        optionsForGet, // Include correct answers and option IDs
				CorrectAnswer:  stringToPointer(question.CorrectAnswer),
				TopicName:      question.Topic.Name,
				DifficultyName: question.Difficulty.Name,
				SubjectName:    question.Topic.Chapter.Subject.Name,
			}
			questions = append(questions, questionInfo)
		}

		sectionInfo := models.TestAnswerKeySectionAdmin{
			SectionID:   section.ID,
			SectionName: section.Name,
			DisplayName: section.DisplayName,
			Questions:   questions,
		}
		sections = append(sections, sectionInfo)
	}

	duration := time.Since(start)
	slog.Info("Admin answer key retrieved successfully",
		"test_id", test.ID,
		"max_possible_score", maxPossibleScore,
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.TestAnswerKeyForAdmin{
		TestID:           test.ID,
		TestName:         test.Name,
		TestType:         test.TestType.Name,
		MaxPossibleScore: maxPossibleScore,
		Sections:         sections,
		Message:          fmt.Sprintf("Answer key retrieved for test '%s' with %d questions", test.Name, maxPossibleScore),
	}

	return result, nil
}

// stringToPointer converts a string to a pointer to string.
// If the string is empty, it returns nil.
func stringToPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// updateQuestionAttemptCounts updates the attempt counts for a question based on the response evaluation
func (p *DbPlugin) updateQuestionAttemptCounts(tx *gorm.DB, questionID uint, isCorrect bool) error {
	// Update the question's attempt counts using GORM's Update method with expressions
	var updates map[string]interface{}
	if isCorrect {
		updates = map[string]interface{}{
			"correct_attempts": gorm.Expr("correct_attempts + 1"),
		}
	} else {
		updates = map[string]interface{}{
			"incorrect_attempts": gorm.Expr("incorrect_attempts + 1"),
		}
	}

	if err := tx.Model(&models.Question{}).
		Where("id = ?", questionID).
		Updates(updates).Error; err != nil {
		slog.Error("Failed to update question attempt counts",
			"question_id", questionID,
			"is_correct", isCorrect,
			"error", err.Error(),
		)
		return fmt.Errorf("failed to update attempt counts for question %d: %w", questionID, err)
	}

	slog.Debug("Question attempt counts updated",
		"question_id", questionID,
		"is_correct", isCorrect,
	)

	return nil
}
