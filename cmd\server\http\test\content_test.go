package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestGetContentAPI(t *testing.T) {
	// Skip if router is not initialized
	if router == nil {
		t.Skip("Router not initialized - skipping content API test")
		return
	}

	// Clean up before test
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	testSubjects := []string{
		"Physics Content Test " + timestamp,
		"Chemistry Content Test " + timestamp,
		"Mathematics Content Test " + timestamp,
	}

	// Clean up any existing test data
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM videos WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM study_materials WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}
	// Clean up any existing test courses
	db.Exec("DELETE FROM courses_subjects WHERE course_id IN (SELECT id FROM courses WHERE name LIKE ?)", "%Course Test "+timestamp)
	db.Exec("DELETE FROM courses WHERE name LIKE ?", "%Course Test "+timestamp)

	// Create test subjects
	physicsSubject := models.Subject{
		Name:        testSubjects[0],
		DisplayName: "Physics Content Test Display",
	}
	db.Create(&physicsSubject)

	chemistrySubject := models.Subject{
		Name:        testSubjects[1],
		DisplayName: "Chemistry Content Test Display",
	}
	db.Create(&chemistrySubject)

	mathSubject := models.Subject{
		Name:        testSubjects[2],
		DisplayName: "Mathematics Content Test Display",
	}
	db.Create(&mathSubject)

	// Create test courses
	testCourse1 := models.Course{
		Name:           "Physics Course Test " + timestamp,
		Description:    "Test course for Physics",
		Price:          1000,
		Discount:       0.1,
		DurationInDays: 365,
		IsFree:         true,
		CourseType:     "IIT-JEE",
	}
	db.Create(&testCourse1)

	testCourse2 := models.Course{
		Name:           "Chemistry Course Test " + timestamp,
		Description:    "Test course for Chemistry",
		Price:          1500,
		Discount:       0.15,
		DurationInDays: 365,
		IsFree:         true,
		CourseType:     "IIT-JEE",
	}
	db.Create(&testCourse2)

	// Associate subjects with courses
	db.Exec("INSERT INTO courses_subjects (course_id, subject_id) VALUES (?, ?)", testCourse1.ID, physicsSubject.ID)
	db.Exec("INSERT INTO courses_subjects (course_id, subject_id) VALUES (?, ?)", testCourse1.ID, mathSubject.ID)
	db.Exec("INSERT INTO courses_subjects (course_id, subject_id) VALUES (?, ?)", testCourse2.ID, chemistrySubject.ID)

	// Create test student with authentication token
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Test Student Content",
			Email:          "test.content." + timestamp + "@example.com",
			PhoneNumber:    "1234567890" + timestamp[len(timestamp)-3:],
			ContactAddress: "Test Address",
		},
		ParentPhone: "9876543210",
		ParentEmail: "parent.content." + timestamp + "@example.com",
		Institute:   "Test Institute",
		Class:       "12th",
		Stream:      "IIT-JEE",
		CityOrTown:  "Test City",
		State:       "Test State",
		Password:    "testpassword123",
	}

	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err := json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)

	studentToken := studentResponse.Token
	assert.NotEmpty(t, studentToken)

	// Create test chapters
	physicsChapter1 := models.Chapter{
		Name:        "Mechanics " + timestamp,
		DisplayName: "Mechanics Display",
		SubjectID:   physicsSubject.ID,
	}
	db.Create(&physicsChapter1)

	physicsChapter2 := models.Chapter{
		Name:        "Thermodynamics " + timestamp,
		DisplayName: "Thermodynamics Display",
		SubjectID:   physicsSubject.ID,
	}
	db.Create(&physicsChapter2)

	chemistryChapter := models.Chapter{
		Name:        "Organic Chemistry " + timestamp,
		DisplayName: "Organic Chemistry Display",
		SubjectID:   chemistrySubject.ID,
	}
	db.Create(&chemistryChapter)

	mathChapter := models.Chapter{
		Name:        "Calculus " + timestamp,
		DisplayName: "Calculus Display",
		SubjectID:   mathSubject.ID,
	}
	db.Create(&mathChapter)

	// Create test videos
	physicsVideo1 := models.Video{
		Name:        "mechanics_intro_" + timestamp,
		DisplayName: "Introduction to Mechanics",
		VideoUrl:    "https://example.com/mechanics_intro.mp4",
		ViewCount:   150,
		ChapterID:   physicsChapter1.ID,
	}
	db.Create(&physicsVideo1)

	physicsVideo2 := models.Video{
		Name:        "thermodynamics_basics_" + timestamp,
		DisplayName: "Thermodynamics Basics",
		VideoUrl:    "https://example.com/thermodynamics_basics.mp4",
		ViewCount:   200,
		ChapterID:   physicsChapter2.ID,
	}
	db.Create(&physicsVideo2)

	chemistryVideo := models.Video{
		Name:        "organic_reactions_" + timestamp,
		DisplayName: "Organic Reactions",
		VideoUrl:    "https://example.com/organic_reactions.mp4",
		ViewCount:   100,
		ChapterID:   chemistryChapter.ID,
	}
	db.Create(&chemistryVideo)

	// Create test study materials
	physicsMaterial := models.StudyMaterial{
		Name:        "mechanics_notes_" + timestamp,
		DisplayName: "Mechanics Study Notes",
		Url:         "https://example.com/mechanics_notes.pdf",
		ChapterID:   physicsChapter1.ID,
	}
	db.Create(&physicsMaterial)

	chemistryMaterial := models.StudyMaterial{
		Name:        "organic_notes_" + timestamp,
		DisplayName: "Organic Chemistry Notes",
		Url:         "https://example.com/organic_notes.pdf",
		ChapterID:   chemistryChapter.ID,
	}
	db.Create(&chemistryMaterial)

	mathMaterial := models.StudyMaterial{
		Name:        "calculus_notes_" + timestamp,
		DisplayName: "Calculus Study Notes",
		Url:         "https://example.com/calculus_notes.pdf",
		ChapterID:   mathChapter.ID,
	}
	db.Create(&mathMaterial)

	// Test 1: Get all content organized by subjects (no course filter)
	t.Run("GetAllContentBySubjects", func(t *testing.T) {
		resp := authenticatedRequestHelper(http.MethodGet, "/api/content", nil, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var contentResponse models.ContentResponseWithProgress
		err := json.Unmarshal(resp.Body.Bytes(), &contentResponse)
		assert.Nil(t, err)

		// Should return content organized by subjects
		assert.GreaterOrEqual(t, len(contentResponse.Subjects), 3, "Should return at least our 3 test subjects")
		assert.GreaterOrEqual(t, contentResponse.Summary.TotalVideos, 3, "Should return at least our 3 test videos")
		assert.GreaterOrEqual(t, contentResponse.Summary.TotalPdfs, 3, "Should return at least our 3 test materials")

		// Verify each subject has the expected content
		// contentResponse.Subjects is already a map[string]SubjectContentWithProgress

		// Check Physics subject
		if physicsSubject, exists := contentResponse.Subjects[testSubjects[0]]; exists {
			assert.Equal(t, 2, physicsSubject.VideoCount, "Physics should have 2 videos")
			assert.Equal(t, 1, physicsSubject.PdfCount, "Physics should have 1 PDF")
			assert.Equal(t, 2, len(physicsSubject.Videos), "Physics videos array should have 2 items")
			assert.Equal(t, 1, len(physicsSubject.Pdfs), "Physics PDFs array should have 1 item")
		}

		// Check Chemistry subject
		if chemistrySubject, exists := contentResponse.Subjects[testSubjects[1]]; exists {
			assert.Equal(t, 1, chemistrySubject.VideoCount, "Chemistry should have 1 video")
			assert.Equal(t, 1, chemistrySubject.PdfCount, "Chemistry should have 1 PDF")
		}

		// Check Mathematics subject
		if mathSubject, exists := contentResponse.Subjects[testSubjects[2]]; exists {
			assert.Equal(t, 0, mathSubject.VideoCount, "Mathematics should have 0 videos")
			assert.Equal(t, 1, mathSubject.PdfCount, "Mathematics should have 1 PDF")
		}

		t.Logf("Content response summary: %+v", contentResponse.Summary)
	})

	// Test 2: Get content for specific course (Physics Course)
	t.Run("GetPhysicsCourseContent", func(t *testing.T) {
		url := fmt.Sprintf("/api/content?course_id=%d", testCourse1.ID)
		resp := authenticatedRequestHelper(http.MethodGet, url, nil, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var contentResponse models.ContentResponseWithProgress
		err := json.Unmarshal(resp.Body.Bytes(), &contentResponse)
		assert.Nil(t, err)

		// Should return content for subjects in the course (Physics and Mathematics)
		assert.Equal(t, 2, len(contentResponse.Subjects), "Should return Physics and Mathematics subjects")
		assert.Equal(t, 2, contentResponse.Summary.TotalVideos, "Course should have 2 videos (from Physics)")
		assert.Equal(t, 2, contentResponse.Summary.TotalPdfs, "Course should have 2 study materials (1 from Physics, 1 from Math)")

		physicsSubject := contentResponse.Subjects[testSubjects[0]]
		assert.Equal(t, 2, physicsSubject.VideoCount, "Physics should have 2 videos")
		assert.Equal(t, 1, physicsSubject.PdfCount, "Physics should have 1 PDF")

		mathSubject := contentResponse.Subjects[testSubjects[2]]
		assert.Equal(t, 0, mathSubject.VideoCount, "Mathematics should have 0 videos")
		assert.Equal(t, 1, mathSubject.PdfCount, "Mathematics should have 1 PDF")

		// Verify videos are from Physics subject
		videoNames := make(map[string]bool)
		for _, video := range physicsSubject.Videos {
			videoNames[video.Name] = true
		}
		assert.True(t, videoNames["mechanics_intro_"+timestamp], "Should contain mechanics video")
		assert.True(t, videoNames["thermodynamics_basics_"+timestamp], "Should contain thermodynamics video")

		// Verify study material is from Physics subject
		materialNames := make(map[string]bool)
		for _, material := range physicsSubject.Pdfs {
			materialNames[material.Name] = true
		}
		assert.True(t, materialNames["mechanics_notes_"+timestamp], "Should contain mechanics notes")

		// Verify video structure (using VideoForGet)
		var mechanicsVideo *models.VideoForGet
		for _, video := range physicsSubject.Videos {
			if video.Name == "mechanics_intro_"+timestamp {
				mechanicsVideo = &video
				break
			}
		}
		assert.NotNil(t, mechanicsVideo, "Should find mechanics video")
		assert.Equal(t, "Introduction to Mechanics", mechanicsVideo.DisplayName)
		assert.Equal(t, "https://example.com/mechanics_intro.mp4", mechanicsVideo.VideoUrl)
		assert.Equal(t, uint(150), mechanicsVideo.ViewCount)
		assert.Equal(t, physicsChapter1.ID, mechanicsVideo.ChapterID)

		// Verify study material structure (using StudyMaterialForGet)
		var mechanicsMaterial *models.StudyMaterialForGet
		for _, material := range physicsSubject.Pdfs {
			if material.Name == "mechanics_notes_"+timestamp {
				mechanicsMaterial = &material
				break
			}
		}
		assert.NotNil(t, mechanicsMaterial, "Should find mechanics material")
		assert.Equal(t, "Mechanics Study Notes", mechanicsMaterial.DisplayName)
		assert.Equal(t, "https://example.com/mechanics_notes.pdf", mechanicsMaterial.Url)
		assert.Equal(t, physicsChapter1.ID, mechanicsMaterial.ChapterID)
	})

	// Test 3: Get content for non-existent course
	t.Run("GetNonExistentCourseContent", func(t *testing.T) {
		url := "/api/content?course_id=99999"
		resp := authenticatedRequestHelper(http.MethodGet, url, nil, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var contentResponse models.ContentResponseWithProgress
		err := json.Unmarshal(resp.Body.Bytes(), &contentResponse)
		assert.Nil(t, err)

		// Should return empty response for non-existent course
		assert.Equal(t, 0, len(contentResponse.Subjects), "Should return empty subjects list for non-existent course")
		assert.Equal(t, 0, contentResponse.Summary.TotalVideos, "Should return 0 total videos for non-existent course")
		assert.Equal(t, 0, contentResponse.Summary.TotalPdfs, "Should return 0 total PDFs for non-existent course")
	})

	// Test 4: Get content for Chemistry course
	t.Run("GetChemistryCourseContent", func(t *testing.T) {
		url := fmt.Sprintf("/api/content?course_id=%d", testCourse2.ID)
		resp := authenticatedRequestHelper(http.MethodGet, url, nil, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var contentResponse models.ContentResponseWithProgress
		err := json.Unmarshal(resp.Body.Bytes(), &contentResponse)
		assert.Nil(t, err)

		// Should return only Chemistry content organized by subject
		assert.Equal(t, 1, len(contentResponse.Subjects), "Should return only Chemistry subject")
		assert.Equal(t, 1, contentResponse.Summary.TotalVideos, "Chemistry should have 1 video")
		assert.Equal(t, 1, contentResponse.Summary.TotalPdfs, "Chemistry should have 1 study material")

		chemistrySubject := contentResponse.Subjects[testSubjects[1]]
		assert.Equal(t, 1, chemistrySubject.VideoCount, "Chemistry should have 1 video")
		assert.Equal(t, 1, chemistrySubject.PdfCount, "Chemistry should have 1 PDF")

		// Verify video is from Chemistry subject
		video := chemistrySubject.Videos[0]
		assert.Equal(t, "organic_reactions_"+timestamp, video.Name)
		assert.Equal(t, "Organic Reactions", video.DisplayName)
		assert.Equal(t, "https://example.com/organic_reactions.mp4", video.VideoUrl)
		assert.Equal(t, uint(100), video.ViewCount)
		assert.Equal(t, chemistryChapter.ID, video.ChapterID)

		// Verify study material is from Chemistry subject
		material := chemistrySubject.Pdfs[0]
		assert.Equal(t, "organic_notes_"+timestamp, material.Name)
		assert.Equal(t, "Organic Chemistry Notes", material.DisplayName)
		assert.Equal(t, "https://example.com/organic_notes.pdf", material.Url)
		assert.Equal(t, chemistryChapter.ID, material.ChapterID)
	})

	// Test 5: Get content with invalid course_id parameter
	t.Run("GetContentWithInvalidCourseID", func(t *testing.T) {
		url := "/api/content?course_id=invalid"
		resp := authenticatedRequestHelper(http.MethodGet, url, nil, studentToken)
		assert.Equal(t, http.StatusBadRequest, resp.Code)

		var errorResponse map[string]interface{}
		err := json.Unmarshal(resp.Body.Bytes(), &errorResponse)
		assert.Nil(t, err)
		assert.Equal(t, "Invalid course_id parameter", errorResponse["error"])
	})

	// Cleanup
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM videos WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM study_materials WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}
	// Clean up courses
	db.Exec("DELETE FROM courses_subjects WHERE course_id IN (?, ?)", testCourse1.ID, testCourse2.ID)
	db.Exec("DELETE FROM courses WHERE id IN (?, ?)", testCourse1.ID, testCourse2.ID)
}
