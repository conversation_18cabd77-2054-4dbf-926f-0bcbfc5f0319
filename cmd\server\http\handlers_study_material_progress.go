package http

import (
	"fmt"
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// UpdateStudyMaterialProgress godoc
//
//	@Summary		Update Study Material Progress
//	@Description	Update or create study material read progress for the authenticated student
//	@Security       BearerAuth
//	@Tags			study-material-progress
//	@Accept			json
//	@Produce		json
//	@Param			progress	body		models.StudyMaterialProgressForUpdate	true	"Study material progress data"
//	@Success		200
//	@Failure		400			{object}	HTTPError
//	@Failure		401			{object}	HTTPError
//	@Failure		404			{object}	HTTPError
//	@Failure		500			{object}	HTTPError
//	@Router			/study-material-progress [put]
func (h *Handlers) UpdateStudyMaterialProgress(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Get student ID from JWT token
	userID, exists := ctx.Get("user_id")
	if !exists {
		slog.Error("UpdateStudyMaterialProgress failed - userID not found in context",
			"client_ip", clientIP,
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get student ID from user ID (convert from float64 to uint)
	userIDFloat, ok := userID.(float64)
	if !ok {
		slog.Error("UpdateStudyMaterialProgress failed - invalid userID type",
			"client_ip", clientIP,
			"userID_type", fmt.Sprintf("%T", userID),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
		return
	}

	studentID, err := h.db.GetStudentIDByUserID(ctx.Request.Context(), uint(userIDFloat))
	if err != nil {
		duration := time.Since(start)
		slog.Error("UpdateStudyMaterialProgress failed - student not found",
			"client_ip", clientIP,
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Student not found"})
		return
	}

	var progressData models.StudyMaterialProgressForUpdate
	if err := ctx.ShouldBindJSON(&progressData); err != nil {
		duration := time.Since(start)
		slog.Error("UpdateStudyMaterialProgress failed - invalid request body",
			"client_ip", clientIP,
			"student_id", studentID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Info("UpdateStudyMaterialProgress request started",
		"client_ip", clientIP,
		"student_id", studentID,
		"study_material_id", progressData.StudyMaterialID,
	)

	err = h.db.UpdateStudyMaterialProgress(ctx.Request.Context(), studentID, &progressData)
	if err != nil {
		duration := time.Since(start)
		slog.Error("UpdateStudyMaterialProgress failed - database error",
			"client_ip", clientIP,
			"student_id", studentID,
			"study_material_id", progressData.StudyMaterialID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("UpdateStudyMaterialProgress successful",
		"client_ip", clientIP,
		"student_id", studentID,
		"study_material_id", progressData.StudyMaterialID,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"message": "Study material progress updated successfully"})
}
