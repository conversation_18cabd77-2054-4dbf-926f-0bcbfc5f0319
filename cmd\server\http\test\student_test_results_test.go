package test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestGetStudentTestResultsEnhanced(t *testing.T) {
	// This test verifies the enhanced GetStudentTestResponses API
	// that returns test results with scores, question statistics, and rankings
	// It uses the existing test infrastructure from the integration test

	// Run migration to add question statistics columns
	migrations := []string{
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS total_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS attempted_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS unattempted_questions INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS correct_answers INTEGER DEFAULT 0 NOT NULL",
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS incorrect_answers INTEGER DEFAULT 0 NOT NULL",
	}

	for _, migration := range migrations {
		if err := db.Exec(migration).Error; err != nil {
			t.Logf("Migration warning: %s, error: %v", migration, err)
		}
	}

	timestamp := fmt.Sprintf("%d", time.Now().UnixNano())
	courseName := "TestCourse_" + timestamp
	testName := "TestMark_" + timestamp
	subjectName := "TestSubject_" + timestamp
	sectionTypeName := "TestSection_" + timestamp
	testTypeName := "TestType_" + timestamp
	chapterName := "TestChapter_" + timestamp
	topicName := "TestTopic_" + timestamp
	difficultyName := "TestDifficulty_" + timestamp

	// Clean up before test
	defer func() {
		db.Exec("DELETE FROM student_test_marks WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM sections_questions WHERE section_id IN (SELECT id FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?))", testName)
		db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		db.Exec("DELETE FROM courses_tests WHERE course_id IN (SELECT id FROM courses WHERE name = ?)", courseName)
		db.Exec("DELETE FROM tests WHERE name = ?", testName)
		db.Exec("DELETE FROM test_type_section_types WHERE test_type_id IN (SELECT id FROM test_types WHERE name = ?)", testTypeName)
		db.Exec("DELETE FROM test_types WHERE name = ?", testTypeName)
		db.Exec("DELETE FROM section_types WHERE name = ?", sectionTypeName)
		db.Exec("DELETE FROM courses WHERE name = ?", courseName)
		db.Exec("DELETE FROM questions WHERE text LIKE ?", "%"+timestamp+"%")
		db.Exec("DELETE FROM difficulties WHERE name = ?", difficultyName)
		db.Exec("DELETE FROM topics WHERE name = ?", topicName)
		db.Exec("DELETE FROM chapters WHERE name = ?", chapterName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
		db.Exec("DELETE FROM users WHERE full_name LIKE ?", "%"+timestamp+"%")
	}()

	// Create test entities using direct database operations (like the integration test)
	subject := models.Subject{Name: subjectName, DisplayName: "Test Subject"}
	db.Create(&subject)

	chapter := models.Chapter{Name: chapterName, DisplayName: "Test Chapter", SubjectID: subject.ID}
	db.Create(&chapter)

	topic := models.Topic{Name: topicName, ChapterID: chapter.ID}
	db.Create(&topic)

	difficulty := models.Difficulty{Name: difficultyName}
	db.Create(&difficulty)

	sectionType := models.SectionType{Name: sectionTypeName, PositiveMarks: 4, NegativeMarks: 1}
	db.Create(&sectionType)

	testType := models.TestType{Name: testTypeName}
	db.Create(&testType)

	// Create test
	test := models.Test{
		Name:        testName,
		TestTypeID:  testType.ID,
		FromTime:    time.Now(),
		ToTime:      time.Now().Add(2 * time.Hour),
		Active:      true,
		Description: "Test for enhanced API",
	}
	db.Create(&test)

	// Create questions
	question1 := models.Question{
		Text:          "What is 2+2? " + timestamp,
		QuestionType:  "MCQ",
		TopicID:       topic.ID,
		DifficultyID:  difficulty.ID,
		CorrectAnswer: "4",
		Options: []models.Option{
			{OptionText: "3", IsCorrect: false},
			{OptionText: "4", IsCorrect: true},
			{OptionText: "5", IsCorrect: false},
			{OptionText: "6", IsCorrect: false},
		},
	}
	db.Create(&question1)

	question2 := models.Question{
		Text:          "What is 3+3? " + timestamp,
		QuestionType:  "MCQ",
		TopicID:       topic.ID,
		DifficultyID:  difficulty.ID,
		CorrectAnswer: "6",
		Options: []models.Option{
			{OptionText: "5", IsCorrect: false},
			{OptionText: "6", IsCorrect: true},
			{OptionText: "7", IsCorrect: false},
			{OptionText: "8", IsCorrect: false},
		},
	}
	db.Create(&question2)

	// Create section and add questions
	section := models.Section{
		Name:          testName + "_section",
		TestID:        test.ID,
		SectionTypeID: sectionType.ID,
	}
	db.Create(&section)

	// Add questions to section using many2many relationship
	section.Questions = []models.Question{question1, question2}
	db.Save(&section)

	// Create student
	user := models.User{
		FullName:    "Test Student " + timestamp,
		Email:       "teststudent" + timestamp + "@example.com",
		PhoneNumber: "1234567890" + timestamp[len(timestamp)-3:],
	}
	db.Create(&user)

	student := models.Student{
		UserID:      user.ID,
		ParentPhone: "9876543210" + timestamp[len(timestamp)-3:],
		Institute:   "Test Institute",
		Class:       "12th",
		Stream:      "Science",
		CityOrTown:  "Test City",
		State:       "Test State",
	}
	db.Create(&student)

	// Create test responses (1 correct, 1 incorrect)
	var correctOption1ID, incorrectOption2ID uint
	for _, option := range question1.Options {
		if option.IsCorrect {
			correctOption1ID = option.ID
			break
		}
	}
	for _, option := range question2.Options {
		if !option.IsCorrect {
			incorrectOption2ID = option.ID
			break
		}
	}

	// Create test responses
	response1 := models.TestResponse{
		TestID:            test.ID,
		StudentID:         student.ID,
		QuestionID:        question1.ID,
		SelectedOptionIDs: models.IntArray{int(correctOption1ID)},
		IsCorrect:         true,
	}
	db.Create(&response1)

	response2 := models.TestResponse{
		TestID:            test.ID,
		StudentID:         student.ID,
		QuestionID:        question2.ID,
		SelectedOptionIDs: models.IntArray{int(incorrectOption2ID)},
		IsCorrect:         false,
	}
	db.Create(&response2)

	// Create StudentTestMark record (this is what the enhanced API reads from)
	subjectMarks := models.SubjectMarksMap{
		subjectName: models.SubjectMarks{
			PositiveMarks:        4,
			NegativeMarks:        1,
			TotalQuestions:       2,
			AttemptedQuestions:   2,
			UnattemptedQuestions: 0,
			CorrectAnswers:       1,
			IncorrectAnswers:     1,
			TopicMarks:           make(map[string]models.TopicMarks),
		},
	}

	studentTestMark := models.StudentTestMark{
		StudentID:            int(student.ID),
		TestID:               int(test.ID),
		TotalPositiveMarks:   4,
		TotalNegativeMarks:   1,
		FinalMarks:           3,
		TotalQuestions:       2,
		AttemptedQuestions:   2,
		UnattemptedQuestions: 0,
		CorrectAnswers:       1,
		IncorrectAnswers:     1,
		SubjectMarks:         subjectMarks,
	}
	db.Create(&studentTestMark)

	// Test the enhanced GetStudentTestResponses API by calling the database function directly
	result, err := server.GetStudentTestResponses(context.Background(), test.ID, student.ID)
	assert.Nil(t, err)
	assert.NotNil(t, result)

	// Verify the enhanced response structure
	t.Logf("Test Results: %+v", result)

	// Verify basic information
	assert.Equal(t, test.ID, result.TestID)
	assert.Equal(t, testName, result.TestName)
	assert.Equal(t, student.ID, result.StudentID)
	assert.Equal(t, user.FullName, result.StudentName)

	// Verify overall score (1 correct * 4 points - 1 incorrect * 1 point = 3)
	assert.Equal(t, 3, result.OverallScore)

	// Verify overall question statistics
	assert.Equal(t, 2, result.OverallQuestions.Total)
	assert.Equal(t, 2, result.OverallQuestions.Attempted)
	assert.Equal(t, 0, result.OverallQuestions.Unattempted)
	assert.Equal(t, 1, result.OverallQuestions.Correct)
	assert.Equal(t, 1, result.OverallQuestions.Incorrect)

	// Verify subject-wise scores
	assert.NotNil(t, result.SubjectWiseScores)
	assert.True(t, len(result.SubjectWiseScores) > 0, "Should have subject-wise scores")

	subjectScore, exists := result.SubjectWiseScores[subjectName]
	assert.True(t, exists, "Should have score for subject: "+subjectName)
	assert.Equal(t, 4, subjectScore.PositiveMarks)
	assert.Equal(t, 1, subjectScore.NegativeMarks)
	assert.Equal(t, 3, subjectScore.FinalMarks)
	assert.Equal(t, 2, subjectScore.Questions.Total)
	assert.Equal(t, 2, subjectScore.Questions.Attempted)
	assert.Equal(t, 0, subjectScore.Questions.Unattempted)
	assert.Equal(t, 1, subjectScore.Questions.Correct)
	assert.Equal(t, 1, subjectScore.Questions.Incorrect)

	// Verify ranking information
	assert.NotNil(t, result.OverallRank, "Should have overall rank")
	assert.Equal(t, 1, *result.OverallRank, "Should be rank 1 (only student)")

	assert.NotNil(t, result.SubjectWiseRanks)
	subjectRank, exists := result.SubjectWiseRanks[subjectName]
	assert.True(t, exists, "Should have subject-wise rank for: "+subjectName)
	assert.Equal(t, 1, subjectRank.Rank, "Should be rank 1 in subject")
	assert.Equal(t, 100.0, subjectRank.Percentile, "Should be 100th percentile")

	t.Logf("✅ Enhanced GetStudentTestResponses API test passed!")
	t.Logf("Overall Score: %d, Rank: %d", result.OverallScore, *result.OverallRank)
	t.Logf("Subject %s: Score=%d, Rank=%d, Percentile=%.1f%%",
		subjectName, subjectScore.FinalMarks, subjectRank.Rank, subjectRank.Percentile)
}
