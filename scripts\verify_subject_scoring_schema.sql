-- Verification script for subject-wise scoring database schema
-- Run this script to verify that all required tables, columns, and indexes are properly configured

\echo '=== Subject-Wise Scoring Schema Verification ==='
\echo ''

-- 1. Check student_test_marks table structure
\echo '1. Checking student_test_marks table structure...'
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    is_generated,
    generation_expression
FROM information_schema.columns
WHERE table_name = 'student_test_marks' 
ORDER BY ordinal_position;

\echo ''

-- 2. Check indexes on student_test_marks
\echo '2. Checking indexes on student_test_marks...'
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'student_test_marks' 
ORDER BY indexname;

\echo ''

-- 3. Check test_responses table for evaluation columns
\echo '3. Checking test_responses table evaluation columns...'
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns
WHERE table_name = 'test_responses' 
AND column_name IN ('calculated_score', 'is_correct')
ORDER BY column_name;

\echo ''

-- 4. Check section_types scoring configuration
\echo '4. Checking section_types scoring columns...'
SELECT 
    column_name, 
    data_type, 
    numeric_precision,
    numeric_scale,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'section_types' 
AND column_name IN ('positive_marks', 'negative_marks')
ORDER BY column_name;

\echo ''

-- 5. Check foreign key relationships
\echo '5. Checking foreign key relationships...'
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name IN ('student_test_marks', 'test_responses', 'section_types')
ORDER BY tc.table_name, kcu.column_name;

\echo ''

-- 6. Test JSONB functionality
\echo '6. Testing JSONB functionality (if data exists)...'
SELECT 
    COUNT(*) as total_records,
    COUNT(subject_marks) as records_with_subject_marks,
    COUNT(subject_marks) FILTER (WHERE subject_marks != '{}') as records_with_data
FROM student_test_marks
WHERE deleted_at IS NULL;

\echo ''

-- 7. Sample JSONB query test (if data exists)
\echo '7. Sample JSONB queries (showing structure)...'
\echo 'Example queries that can be run:'
\echo '  -- Find records with Mathematics subject:'
\echo '  SELECT student_id, test_id FROM student_test_marks WHERE subject_marks ? ''Mathematics'';'
\echo ''
\echo '  -- Get Mathematics positive marks:'
\echo '  SELECT student_id, subject_marks->''Mathematics''->>''positive_marks'' as math_positive'
\echo '  FROM student_test_marks WHERE subject_marks ? ''Mathematics'';'
\echo ''

-- 8. Check view creation (if exists)
\echo '8. Checking if subject_wise_rankings view exists...'
SELECT 
    table_name,
    view_definition
FROM information_schema.views 
WHERE table_name = 'subject_wise_rankings';

\echo ''

-- 9. Performance check - explain a typical ranking query
\echo '9. Query plan for typical ranking query...'
EXPLAIN (COSTS OFF, BUFFERS OFF) 
SELECT student_id, final_marks, 
       ROW_NUMBER() OVER (ORDER BY final_marks DESC) as rank
FROM student_test_marks 
WHERE test_id = 1 
ORDER BY final_marks DESC 
LIMIT 10;

\echo ''

-- 10. Summary
\echo '10. Schema verification summary:'
\echo '✓ All required tables should be present'
\echo '✓ JSONB column should be configured'
\echo '✓ Generated column should be working'
\echo '✓ Indexes should be optimized for ranking queries'
\echo '✓ Foreign keys should maintain referential integrity'
\echo ''
\echo 'Schema verification completed!'

-- Optional: Show sample data structure if any exists
\echo ''
\echo 'Sample data structure (if any records exist):'
SELECT 
    student_id,
    test_id,
    total_positive_marks,
    total_negative_marks,
    final_marks,
    jsonb_pretty(subject_marks) as subject_marks_formatted
FROM student_test_marks 
WHERE deleted_at IS NULL 
AND subject_marks IS NOT NULL 
AND subject_marks != '{}'
LIMIT 3;
