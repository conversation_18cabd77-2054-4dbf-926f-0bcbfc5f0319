package http

import (
	"context"
	"log/slog"
	"net/http"
	"strconv"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// RecordTestResponses godoc
//
//	@Summary		RecordTestResponses
//	@Description	Record student responses for all questions in a test
//	@Security       BearerAuth
//	@Param			item	body	models.TestResponsesForCreate	true	"test responses"
//	@Tags			test-responses
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.TestResponsesResult
//	@Failure		400	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		409	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-responses [post]
func (h *Handlers) RecordTestResponses(ctx *gin.Context) {
	start := time.Now()

	// Get user ID from JWT token (set by middleware)
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		// In test mode (skipAuth=true), token extraction will fail
		// Log this as debug rather than error and return 401 for consistency
		slog.Debug("RecordTestResponses - no token found (likely test mode)",
			"error", err.Error(),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get client IP for logging
	clientIP := ctx.ClientIP()

	slog.Info("RecordTestResponses request started",
		"user_id", userID,
		"client_ip", clientIP,
	)

	// Parse request body
	testResponsesInput := new(models.TestResponsesForCreate)
	if err := ctx.ShouldBindJSON(testResponsesInput); err != nil {
		duration := time.Since(start)
		slog.Warn("RecordTestResponses failed - invalid request body",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate that responses are provided
	if len(testResponsesInput.Responses) == 0 {
		duration := time.Since(start)
		slog.Warn("RecordTestResponses failed - no responses provided",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id", testResponsesInput.TestID,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No responses provided"})
		return
	}

	// Get student ID from user ID
	studentID, err := h.getStudentIDFromUserID(ctx.Request.Context(), userID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("RecordTestResponses failed - failed to get student ID",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only students can submit test responses"})
		return
	}

	slog.Debug("RecordTestResponses processing",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testResponsesInput.TestID,
		"response_count", len(testResponsesInput.Responses),
	)

	// Record the responses
	result, err := h.db.RecordTestResponses(ctx.Request.Context(), studentID, testResponsesInput)
	if err != nil {
		duration := time.Since(start)
		slog.Error("RecordTestResponses failed - database error",
			"user_id", userID,
			"student_id", studentID,
			"client_ip", clientIP,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)

		// Check for specific error types
		if err.Error() == "student has already submitted responses for test ID "+strconv.Itoa(int(testResponsesInput.TestID)) {
			ctx.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	duration := time.Since(start)
	slog.Info("RecordTestResponses successful",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testResponsesInput.TestID,
		"total_questions", result.TotalQuestions,
		"correct_answers", result.CorrectAnswers,
		"total_score", result.TotalScore,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, result)
}

// GetStudentTestResult godoc
//
//	@Summary		GetStudentTestResult
//	@Description	Get test results for the logged in student including overall and subject-wise scores, question statistics, and rankings
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Tags			test-responses
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.StudentTestResult
//	@Failure		400	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-results/{test_id} [get]
func (h *Handlers) GetStudentTestResult(ctx *gin.Context) {
	start := time.Now()

	// Get user ID from JWT token (set by middleware)
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		// In test mode (skipAuth=true), token extraction will fail
		// Log this as debug rather than error and return 401 for consistency
		slog.Debug("GetStudentTestResult - no token found (likely test mode)",
			"error", err.Error(),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get client IP for logging
	clientIP := ctx.ClientIP()

	// Parse test ID from URL parameter
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetStudentTestResult failed - invalid test ID",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id_param", testIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	slog.Info("GetStudentTestResult request started",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", testID,
	)

	// Get student ID from user ID
	studentID, err := h.getStudentIDFromUserID(ctx.Request.Context(), userID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetStudentTestResult failed - failed to get student ID",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only students can view their test responses"})
		return
	}

	slog.Debug("GetStudentTestResult processing",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testID,
	)

	// Get student responses with total score and recording timestamp
	result, err := h.db.GetStudentTestResult(ctx.Request.Context(), studentID, uint(testID))
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetStudentTestResult failed - database error",
			"user_id", userID,
			"student_id", studentID,
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetStudentTestResult successful",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testID,
		"overall_score", result.OverallScore,
		"overall_rank", result.OverallRank,
		"subject_count", len(result.SubjectWiseScores),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, result)
}

// GetTestRankings godoc
//
//	@Summary		GetTestRankings
//	@Description	Get rankings for all students in a specific test - Only meant for admin users
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Param			limit	query	int	false	"Limit number of results (default: 100)"
//	@Param			offset	query	int	false	"Offset for pagination (default: 0)"
//	@Tags			tests
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.TestRankingResult
//	@Failure		400	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/tests/rankings/{test_id} [get]
func (h *Handlers) GetTestRankings(ctx *gin.Context) {
	start := time.Now()

	// Get user ID from JWT token (set by middleware)
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		// In test mode (skipAuth=true), token extraction will fail
		// Log this as debug rather than error and return 401 for consistency
		slog.Debug("GetTestRankings - no token found (likely test mode)",
			"error", err.Error(),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get client IP for logging
	clientIP := ctx.ClientIP()

	slog.Info("GetTestRankings request started",
		"user_id", userID,
		"client_ip", clientIP,
	)

	if err := h.checkAdminPermission(ctx.Request.Context(), userID); err != nil {
		duration := time.Since(start)
		slog.Warn("GetTestRankings failed - user is not an admin",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only admins can view test rankings"})
		return
	}

	// Parse test ID from URL parameter
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetTestRankings failed - invalid test ID",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id_param", testIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	// Parse query parameters
	limitStr := ctx.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 100 // Default limit to prevent huge responses
	}

	offsetStr := ctx.DefaultQuery("offset", "0")
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	slog.Debug("GetTestRankings processing",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", testID,
		"limit", limit,
		"offset", offset,
	)

	// Get the rankings
	result, err := h.db.GetTestRankings(ctx.Request.Context(), uint(testID), limit, offset)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetTestRankings failed - database error",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetTestRankings successful",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", testID,
		"total_students", result.TotalStudents,
		"returned_count", len(result.StudentRankings),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, result)
}

// getStudentIDFromUserID is a helper function to get student ID from user ID
func (h *Handlers) getStudentIDFromUserID(ctx context.Context, userID uint) (uint, error) {
	return h.db.GetStudentIDByUserID(ctx, userID)
}
