package db

import (
	"context"
	"fmt"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEligibleForRankingFunctionality(t *testing.T) {
	// Skip if no test database is available
	if testDB == nil {
		t.Skip("Test database not available")
	}

	// Run migration to add eligible_for_ranking column and duration column
	migrations := []string{
		"ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS eligible_for_ranking BOOLEAN DEFAULT FALSE NOT NULL",
		"CREATE INDEX IF NOT EXISTS idx_student_test_marks_eligible_ranking ON student_test_marks (test_id, eligible_for_ranking, final_marks DESC) WHERE eligible_for_ranking = TRUE",
		"ALTER TABLE tests ADD COLUMN IF NOT EXISTS duration INTEGER",
	}

	for _, migration := range migrations {
		if err := testDB.Exec(migration).Error; err != nil {
			t.Logf("Migration warning: %s, error: %v", migration, err)
		}
	}

	// Generate unique names to avoid conflicts
	timestamp := time.Now().UnixNano()
	subjectName := fmt.Sprintf("EligibleRankingTestSubject_%d", timestamp)
	testName1 := fmt.Sprintf("EligibleRankingTest1_%d", timestamp)
	testName2 := fmt.Sprintf("EligibleRankingTest2_%d", timestamp)
	testName3 := fmt.Sprintf("EligibleRankingTest3_%d", timestamp)

	// Clean up after test
	defer func() {
		testDB.Exec("DELETE FROM student_test_marks WHERE test_id IN (SELECT id FROM tests WHERE name LIKE ?)", fmt.Sprintf("EligibleRankingTest%%_%d", timestamp))
		testDB.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name LIKE ?)", fmt.Sprintf("EligibleRankingTest%%_%d", timestamp))
		testDB.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE ?)", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM users WHERE email LIKE ?", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM questions WHERE topic_id IN (SELECT id FROM topics WHERE name LIKE ?)", fmt.Sprintf("%%EligibleRankingTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE name LIKE ?)", fmt.Sprintf("%%EligibleRankingTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		testDB.Exec("DELETE FROM tests WHERE name LIKE ?", fmt.Sprintf("EligibleRankingTest%%_%d", timestamp))
		testDB.Exec("DELETE FROM test_types WHERE name LIKE ?", fmt.Sprintf("%%EligibleTestType_%d%%", timestamp))
		testDB.Exec("DELETE FROM difficulties WHERE name LIKE ?", fmt.Sprintf("%%EligibleTestDifficulty_%d%%", timestamp))
		testDB.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}()

	// Create test data
	subject := models.Subject{Name: subjectName, DisplayName: "Test Subject"}
	err := testDB.Create(&subject).Error
	require.NoError(t, err)

	chapter := models.Chapter{Name: fmt.Sprintf("EligibleRankingTestChapter_%d", timestamp), DisplayName: "Test Chapter", SubjectID: subject.ID}
	err = testDB.Create(&chapter).Error
	require.NoError(t, err)

	topic := models.Topic{Name: fmt.Sprintf("EligibleRankingTestTopic_%d", timestamp), ChapterID: chapter.ID}
	err = testDB.Create(&topic).Error
	require.NoError(t, err)

	difficulty := models.Difficulty{Name: fmt.Sprintf("EligibleTestDifficulty_%d", timestamp)}
	err = testDB.Create(&difficulty).Error
	require.NoError(t, err)

	// Create test type
	testType := models.TestType{Name: fmt.Sprintf("EligibleTestType_%d", timestamp)}
	err = testDB.Create(&testType).Error
	require.NoError(t, err)

	// Create three tests with different timing scenarios
	now := time.Now()

	// Test 1: Active test (current time between from_time and to_time) - should be eligible
	duration30 := 30 // 30 minutes
	test1 := models.Test{
		Name:        testName1,
		TestTypeID:  testType.ID,
		FromTime:    now.Add(-1 * time.Hour), // 1 hour ago
		ToTime:      now.Add(1 * time.Hour),  // 1 hour from now
		Duration:    &duration30,             // 30 minutes duration
		Active:      true,
		Description: "Test with valid timing - eligible for ranking",
	}
	err = testDB.Create(&test1).Error
	require.NoError(t, err)

	// Test 2: Future test (current time before from_time) - should not be eligible
	test2 := models.Test{
		Name:        testName2,
		TestTypeID:  testType.ID,
		FromTime:    now.Add(1 * time.Hour), // 1 hour from now
		ToTime:      now.Add(2 * time.Hour), // 2 hours from now
		Active:      true,
		Description: "Future test - not eligible for ranking",
	}
	err = testDB.Create(&test2).Error
	require.NoError(t, err)

	// Test 3: Test with missing timing (null from_time/to_time) - should not be eligible
	test3 := models.Test{
		Name:        testName3,
		TestTypeID:  testType.ID,
		Active:      true,
		Description: "Test without timing - not eligible for ranking",
	}
	err = testDB.Create(&test3).Error
	require.NoError(t, err)

	// Create a student
	user := models.User{
		FullName:    fmt.Sprintf("Eligible Test Student_%d", timestamp),
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: fmt.Sprintf("1234567%03d", timestamp%1000),
	}
	err = testDB.Create(&user).Error
	require.NoError(t, err)

	student := models.Student{
		UserID:      user.ID,
		ParentPhone: fmt.Sprintf("9876543%03d", timestamp%1000),
		Institute:   "Test Institute",
		Class:       "12th",
		Stream:      "IIT-JEE",
		CityOrTown:  "Test City",
		State:       "Test State",
	}
	err = testDB.Create(&student).Error
	require.NoError(t, err)

	// Create a question for testing
	question := models.Question{
		Text:          fmt.Sprintf("Test question for eligible ranking_%d", timestamp),
		QuestionType:  "mcq",
		TopicID:       topic.ID,
		DifficultyID:  difficulty.ID,
		CorrectAnswer: "A",
	}
	err = testDB.Create(&question).Error
	require.NoError(t, err)

	// Create options for the question
	options := []models.Option{
		{QuestionID: question.ID, OptionText: "Option A", IsCorrect: true},
		{QuestionID: question.ID, OptionText: "Option B", IsCorrect: false},
	}
	err = testDB.Create(&options).Error
	require.NoError(t, err)

	// Initialize the DbPlugin
	dbPlugin := &DbPlugin{db: testDB}

	// Test the isEligibleForRanking function directly
	t.Run("Test isEligibleForRanking function", func(t *testing.T) {
		// Test 1: Should be eligible (current time between from_time and to_time)
		eligible1 := dbPlugin.isEligibleForRanking(testDB, test1.ID)
		assert.True(t, eligible1, "Test 1 should be eligible for ranking")

		// Test 2: Should not be eligible (future test)
		eligible2 := dbPlugin.isEligibleForRanking(testDB, test2.ID)
		assert.False(t, eligible2, "Test 2 should not be eligible for ranking")

		// Test 3: Should not be eligible (missing timing)
		eligible3 := dbPlugin.isEligibleForRanking(testDB, test3.ID)
		assert.False(t, eligible3, "Test 3 should not be eligible for ranking")
	})

	// Test the createOrUpdateStudentTestMark function with eligible_for_ranking
	t.Run("Test createOrUpdateStudentTestMark with eligible_for_ranking", func(t *testing.T) {
		subjectMarks := models.SubjectMarksMap{
			subjectName: models.SubjectMarks{
				PositiveMarks:        4,
				NegativeMarks:        0,
				TotalQuestions:       1,
				AttemptedQuestions:   1,
				UnattemptedQuestions: 0,
				CorrectAnswers:       1,
				IncorrectAnswers:     0,
				TopicMarks:           make(map[string]models.TopicMarks),
			},
		}

		// Create marks for all three tests
		err = dbPlugin.createOrUpdateStudentTestMark(testDB, student.ID, test1.ID, subjectMarks, 4, 0, 1, 1, 0, 1, 0)
		require.NoError(t, err)

		err = dbPlugin.createOrUpdateStudentTestMark(testDB, student.ID, test2.ID, subjectMarks, 4, 0, 1, 1, 0, 1, 0)
		require.NoError(t, err)

		err = dbPlugin.createOrUpdateStudentTestMark(testDB, student.ID, test3.ID, subjectMarks, 4, 0, 1, 1, 0, 1, 0)
		require.NoError(t, err)

		// Verify the eligible_for_ranking field is set correctly
		var mark1 models.StudentTestMark
		err = testDB.Where("student_id = ? AND test_id = ?", student.ID, test1.ID).First(&mark1).Error
		require.NoError(t, err)
		assert.True(t, mark1.EligibleForRanking, "Test 1 marks should be eligible for ranking")

		var mark2 models.StudentTestMark
		err = testDB.Where("student_id = ? AND test_id = ?", student.ID, test2.ID).First(&mark2).Error
		require.NoError(t, err)
		assert.False(t, mark2.EligibleForRanking, "Test 2 marks should not be eligible for ranking")

		var mark3 models.StudentTestMark
		err = testDB.Where("student_id = ? AND test_id = ?", student.ID, test3.ID).First(&mark3).Error
		require.NoError(t, err)
		assert.False(t, mark3.EligibleForRanking, "Test 3 marks should not be eligible for ranking")
	})

	// Test ranking queries filter by eligible_for_ranking
	t.Run("Test ranking queries filter by eligible_for_ranking", func(t *testing.T) {
		ctx := context.Background()

		// Test GetTestRankings - should only return eligible marks
		rankings1, err := dbPlugin.GetTestRankings(ctx, test1.ID, 0, 0)
		require.NoError(t, err)
		assert.Equal(t, 1, rankings1.TotalStudents, "Test 1 should have 1 eligible student")
		assert.Equal(t, 1, len(rankings1.StudentRankings), "Test 1 should return 1 ranking")

		rankings2, err := dbPlugin.GetTestRankings(ctx, test2.ID, 0, 0)
		require.NoError(t, err)
		assert.Equal(t, 0, rankings2.TotalStudents, "Test 2 should have 0 eligible students")
		assert.Equal(t, 0, len(rankings2.StudentRankings), "Test 2 should return 0 rankings")

		rankings3, err := dbPlugin.GetTestRankings(ctx, test3.ID, 0, 0)
		require.NoError(t, err)
		assert.Equal(t, 0, rankings3.TotalStudents, "Test 3 should have 0 eligible students")
		assert.Equal(t, 0, len(rankings3.StudentRankings), "Test 3 should return 0 rankings")
	})
}

func TestEligibleForRankingWithDuration(t *testing.T) {
	ctx := context.Background()
	timestamp := time.Now().Unix()

	// Create test data
	subject := models.Subject{Name: fmt.Sprintf("DurationTestSubject_%d", timestamp)}
	err := testDB.Create(&subject).Error
	require.NoError(t, err)

	difficulty := models.Difficulty{Name: fmt.Sprintf("DurationTestDifficulty_%d", timestamp)}
	err = testDB.Create(&difficulty).Error
	require.NoError(t, err)

	testType := models.TestType{Name: fmt.Sprintf("DurationTestType_%d", timestamp)}
	err = testDB.Create(&testType).Error
	require.NoError(t, err)

	now := time.Now()
	duration60 := 60 // 60 minutes

	// Test case: Test where current time is after ToTime but within ToTime + Duration
	// This should be eligible for ranking due to the duration extension
	testName := fmt.Sprintf("DurationExtensionTest_%d", timestamp)
	test := models.Test{
		Name:        testName,
		TestTypeID:  testType.ID,
		FromTime:    now.Add(-2 * time.Hour),    // 2 hours ago
		ToTime:      now.Add(-30 * time.Minute), // 30 minutes ago (past ToTime)
		Duration:    &duration60,                // 60 minutes duration, so effective end time is 30 minutes from now
		Active:      true,
		Description: "Test with duration extension - should be eligible",
	}
	err = testDB.Create(&test).Error
	require.NoError(t, err)

	t.Run("Test eligibility with duration extension", func(t *testing.T) {
		// Test the isEligibleForRanking function directly
		tx := testDB.Begin()
		defer tx.Rollback()

		isEligible := dbPlugin.isEligibleForRanking(tx, test.ID)
		assert.True(t, isEligible, "Test should be eligible due to duration extension")

		// Create a user and student to verify the eligibility is set correctly
		user := models.User{
			FullName:    fmt.Sprintf("DurationTestUser_%d", timestamp),
			Email:       fmt.Sprintf("<EMAIL>", timestamp),
			PhoneNumber: fmt.Sprintf("555000%04d", timestamp%10000),
		}
		err = testDB.Create(&user).Error
		require.NoError(t, err)

		student := models.Student{
			UserID:      user.ID,
			ParentPhone: fmt.Sprintf("555111%04d", timestamp%10000),
			Institute:   "Test Institute",
			Class:       "12th",
			Stream:      "IIT-JEE",
			CityOrTown:  "Test City",
			State:       "Test State",
		}
		err = testDB.Create(&student).Error
		require.NoError(t, err)

		// Create a chapter for the topic
		chapter := models.Chapter{Name: fmt.Sprintf("DurationTestChapter_%d", timestamp), SubjectID: subject.ID}
		err = testDB.Create(&chapter).Error
		require.NoError(t, err)

		// Create a topic for the question
		topic := models.Topic{Name: fmt.Sprintf("DurationTestTopic_%d", timestamp), ChapterID: chapter.ID}
		err = testDB.Create(&topic).Error
		require.NoError(t, err)

		// Create a section type
		sectionType := models.SectionType{
			Name:          fmt.Sprintf("DurationTestSectionType_%d", timestamp),
			SubjectID:     subject.ID,
			QuestionCount: 1,
			PositiveMarks: 4.0,
			NegativeMarks: 1.0,
		}
		err = testDB.Create(&sectionType).Error
		require.NoError(t, err)

		// Associate section type with test type
		err = testDB.Model(&testType).Association("SectionTypes").Append(&sectionType)
		require.NoError(t, err)

		// Create a section for the test
		section := models.Section{
			Name:          fmt.Sprintf("DurationTestSection_%d", timestamp),
			TestID:        test.ID,
			SectionTypeID: sectionType.ID,
		}
		err = testDB.Create(&section).Error
		require.NoError(t, err)

		// Create a question for the test
		question := models.Question{
			Text:         fmt.Sprintf("Duration test question_%d", timestamp),
			TopicID:      topic.ID,
			DifficultyID: difficulty.ID,
			QuestionType: "mcq",
		}
		err = testDB.Create(&question).Error
		require.NoError(t, err)

		// Associate question with section
		err = testDB.Model(&section).Association("Questions").Append(&question)
		require.NoError(t, err)

		// Record test responses to trigger mark calculation
		responses := models.TestResponsesForCreate{
			TestID: test.ID,
			Responses: []models.TestResponseForCreate{
				{
					QuestionID:        question.ID,
					SelectedOptionIDs: []int{},
					ResponseText:      nil,
				},
			},
		}

		_, err = dbPlugin.RecordTestResponses(ctx, student.ID, &responses)
		require.NoError(t, err)

		// Verify that the student test mark was created with eligible_for_ranking = true
		var studentTestMark models.StudentTestMark
		err = testDB.Where("student_id = ? AND test_id = ?", student.ID, test.ID).First(&studentTestMark).Error
		require.NoError(t, err)
		assert.True(t, studentTestMark.EligibleForRanking, "Student test mark should be eligible for ranking due to duration extension")
	})
}
