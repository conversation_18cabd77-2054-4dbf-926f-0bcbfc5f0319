-- Migration to add GIN index on subject_marks JSONB column for improved query performance
-- This index will help with queries that filter or search within the subject_marks JSONB data
-- Run this script to optimize subject-wise ranking queries

-- Add GIN index on subject_marks JSONB column to support efficient queries on subject data
CREATE INDEX IF NOT EXISTS idx_student_test_marks_subject_marks_gin ON student_test_marks USING GIN (subject_marks);

-- Verify the index was created
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'student_test_marks' 
AND indexname = 'idx_student_test_marks_subject_marks_gin';

-- Example queries that will benefit from this index:
-- 1. Find students who have marks in a specific subject:
--    SELECT * FROM student_test_marks WHERE subject_marks ? 'Mathematics';
--
-- 2. Find students with specific subject score conditions:
--    SELECT * FROM student_test_marks WHERE subject_marks @> '{"Mathematics": {"positive_marks": 10}}';
--
-- 3. Extract subject-wise data efficiently:
--    SELECT student_id, test_id, subject_marks->'Mathematics' as math_marks 
--    FRO<PERSON> student_test_marks WHERE subject_marks ? 'Mathematics';
