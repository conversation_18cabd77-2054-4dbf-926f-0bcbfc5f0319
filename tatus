[1mdiff --git a/internal/models/models.go b/internal/models/models.go[m
[1mindex 8ec6c5e..a1dd3cb 100644[m
[1m--- a/internal/models/models.go[m
[1m+++ b/internal/models/models.go[m
[36m@@ -2,6 +2,7 @@[m [mpackage models[m
 [m
 import ([m
 	"database/sql/driver"[m
[32m+[m	[32m"encoding/json"[m
 	"fmt"[m
 	"strconv"[m
 	"strings"[m
[36m@@ -66,6 +67,34 @@[m [mfunc (a *IntArray) scanString(s string) error {[m
 	return nil[m
 }[m
 [m
[32m+[m[32m// SubjectMarksMap is a custom type for JSONB storage of subject marks[m
[32m+[m[32mtype SubjectMarksMap map[string]SubjectMarks[m
[32m+[m
[32m+[m[32m// Value implements the driver.Valuer interface for database storage[m
[32m+[m[32mfunc (sm SubjectMarksMap) Value() (driver.Value, error) {[m
[32m+[m	[32mif sm == nil {[m
[32m+[m		[32mreturn nil, nil[m
[32m+[m	[32m}[m
[32m+[m	[32mreturn json.Marshal(sm)[m
[32m+[m[32m}[m
[32m+[m
[32m+[m[32m// Scan implements the sql.Scanner interface for database retrieval[m
[32m+[m[32mfunc (sm *SubjectMarksMap) Scan(value interface{}) error {[m
[32m+[m	[32mif value == nil {[m
[32m+[m		[32m*sm = make(SubjectMarksMap)[m
[32m+[m		[32mreturn nil[m
[32m+[m	[32m}[m
[32m+[m
[32m+[m	[32mswitch v := value.(type) {[m
[32m+[m	[32mcase []byte:[m
[32m+[m		[32mreturn json.Unmarshal(v, sm)[m
[32m+[m	[32mcase string:[m
[32m+[m		[32mreturn json.Unmarshal([]byte(v), sm)[m
[32m+[m	[32mdefault:[m
[32m+[m		[32mreturn fmt.Errorf("cannot scan %T into SubjectMarksMap", value)[m
[32m+[m	[32m}[m
[32m+[m[32m}[m
[32m+[m
 type User struct {[m
 	gorm.Model[m
 	FullName       string `gorm:"not null"`[m
[36m@@ -687,15 +716,15 @@[m [mtype TopicMarks struct {[m
 }[m
 [m
 type StudentTestMark struct {[m
[31m-	StudentID          int                     `gorm:"primaryKey;not null"`[m
[31m-	TestID             int                     `gorm:"primaryKey;not null;index:idx_test_final_marks_desc"`[m
[31m-	TotalPositiveMarks int                     `gorm:"default:0;not null"`[m
[31m-	TotalNegativeMarks int                     `gorm:"default:0;not null"`[m
[31m-	FinalMarks         int                     `gorm:"->;type:integer;not null;index:idx_test_final_marks_desc,sort:desc"` // Read-only; generated column with test-specific descending index[m
[31m-	SubjectMarks       map[string]SubjectMarks `gorm:"type:jsonb"`                                                         // subject_name -> SubjectMarks[m
[31m-	CreatedAt          time.Time               `gorm:"autoCreateTime"`[m
[31m-	UpdatedAt          time.Time               `gorm:"autoUpdateTime"`[m
[31m-	DeletedAt          *time.Time              `gorm:"index"`[m
[32m+[m	[32mStudentID          int             `gorm:"primaryKey;not null"`[m
[32m+[m	[32mTestID             int             `gorm:"primaryKey;not null;index:idx_test_final_marks_desc"`[m
[32m+[m	[32mTotalPositiveMarks int             `gorm:"default:0;not null"`[m
[32m+[m	[32mTotalNegativeMarks int             `gorm:"default:0;not null"`[m
[32m+[m	[32mFinalMarks         int             `gorm:"->;type:integer;not null;index:idx_test_final_marks_desc,sort:desc"` // Read-only; generated column with test-specific descending index[m
[32m+[m	[32mSubjectMarks       SubjectMarksMap `gorm:"type:jsonb"`                                                         // subject_name -> SubjectMarks[m
[32m+[m	[32mCreatedAt          time.Time       `gorm:"autoCreateTime"`[m
[32m+[m	[32mUpdatedAt          time.Time       `gorm:"autoUpdateTime"`[m
[32m+[m	[32mDeletedAt          *time.Time      `gorm:"index"`[m
 [m
 	Student Student `gorm:"foreignKey:StudentID;constraint:OnDelete:CASCADE"`[m
 	Test    Test    `gorm:"foreignKey:TestID;constraint:OnDelete:CASCADE"`[m
[36m@@ -870,14 +899,27 @@[m [mtype StudentTestResponsesResult struct {[m
 [m
 // StudentRankingInfo represents a student's ranking information for a specific test[m
 type StudentRankingInfo struct {[m
[31m-	StudentID          uint    `json:"student_id"`[m
[31m-	StudentName        string  `json:"student_name"`[m
[31m-	StudentEmail       string  `json:"student_email"`[m
[31m-	TotalPositiveMarks int     `json:"total_positive_marks"`[m
[31m-	TotalNegativeMarks int     `json:"total_negative_marks"`[m
[31m-	FinalMarks         int     `json:"final_marks"`[m
[31m-	Rank               int     `json:"rank"`[m
[31m-	Percentile         float64 `json:"percentile"`[m
[32m+[m	[32mStudentID          uint                       `json:"student_id"`[m
[32m+[m	[32mStudentName        string                     `json:"student_name"`[m
[32m+[m	[32mStudentEmail       string                     `json:"student_email"`[m
[32m+[m	[32mTotalPositiveMarks int                        `json:"total_positive_marks"`[m
[32m+[m	[32mTotalNegativeMarks int                        `json:"total_negative_marks"`[m
[32m+[m	[32mFinalMarks         int                        `json:"final_marks"`[m
[32m+[m	[32mRank               int                        `json:"rank"`[m
[32m+[m	[32mPercentile         float64                    `json:"percentile"`[m
[32m+[m	[32mSubjectRanks       map[string]SubjectRankInfo `json:"subject_ranks"` // subject_name -> SubjectRankInfo[m
[32m+[m[32m}[m
[32m+[m
[32m+[m[32m// SubjectRankInfo represents a student's ranking information for a specific subject in a test[m
[32m+[m[32mtype SubjectRankInfo struct {[m
[32m+[m	[32mStudentID     uint    `json:"student_id"`[m
[32m+[m	[32mSubjectName   string  `json:"subject_name"`[m
[32m+[m	[32mPositiveMarks int     `json:"positive_marks"`[m
[32m+[m	[32mNegativeMarks int     `json:"negative_marks"`[m
[32m+[m	[32mFinalMarks    int     `json:"final_marks"`[m
[32m+[m	[32mRank          int     `json:"rank"`[m
[32m+[m	[32mPercentile    float64 `json:"percentile"`[m
[32m+[m	[32mTotalStudents int     `json:"total_students"` // Total students who attempted this subject[m
 }[m
 [m
 // SubjectScoreInfo represents subject-wise scoring information[m
[36m@@ -899,6 +941,20 @@[m [mtype TestRankingResult struct {[m
 	Message         string                      `json:"message"`[m
 }[m
 [m
[32m+[m[32m// SubjectRankingResult represents the ranking results for a specific subject in a test[m
[32m+[m[32mtype SubjectRankingResult struct {[m
[32m+[m	[32mTestID        uint               `json:"test_id"`[m
[32m+[m	[32mTestName      string             `json:"test_name"`[m
[32m+[m	[32mSubjectName   string             `json:"subject_name"`[m
[32m+[m	[32mTotalStudents int                `json:"total_students"`[m
[32m+[m	[32mHighestMarks  int                `json:"highest_marks"`[m
[32m+[m	[32mLowestMarks   int                `json:"lowest_marks"`[m
[32m+[m	[32mAverageMarks  float64            `json:"average_marks"`[m
[32m+[m	[32mRankings      []SubjectRankInfo  `json:"rankings"`[m
[32m+[m	[32mTopicScores   map[string]float64 `json:"topic_scores"` // topic_name -> average_score[m
[32m+[m	[32mMessage       string             `json:"message"`[m
[32m+[m[32m}[m
[32m+[m
 // TestQuestionInfo represents a question within a test section[m
 type TestQuestionInfo struct {[m
 	ID             uint     `json:"id"`[m
