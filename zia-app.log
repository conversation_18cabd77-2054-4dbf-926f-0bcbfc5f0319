time=2025-09-01T20:05:16.600+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-09-01T20:05:16.601+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-09-01T20:05:16.601+05:30 level=INFO msg="CORS middleware configured" allowed_origins="[http://localhost:3000 http://localhost:3001 https://localhost:3000 https://localhost:3001 https://zac-admin-seven.vercel.app]" allowed_methods="[GET POST PUT DELETE OPTIONS PATCH]" allow_credentials=true
time=2025-09-01T20:05:16.603+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (5 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (5 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (5 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (5 handlers)
[GIN-debug] POST   /api/webhook/razorpay     --> ziaacademy-backend/cmd/server/http.(*Handlers).RazorpayWebhookHandler-fm (5 handlers)
time=2025-09-01T20:05:16.603+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (6 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (6 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (6 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (6 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (6 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (6 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (6 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (6 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (6 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (6 handlers)
[GIN-debug] POST   /api/institutions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateInstitution-fm (6 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (6 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (6 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (6 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (6 handlers)
[GIN-debug] GET    /api/institutions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetInstitutions-fm (6 handlers)
[GIN-debug] PUT    /api/institutions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateInstitution-fm (6 handlers)
[GIN-debug] DELETE /api/institutions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).DeleteInstitution-fm (6 handlers)
[GIN-debug] GET    /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudents-fm (6 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (6 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (6 handlers)
[GIN-debug] GET    /api/courses/:id          --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourseByID-fm (6 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (6 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (6 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (6 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (6 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (6 handlers)
[GIN-debug] GET    /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAdminUsers-fm (6 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (6 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (6 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (6 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (6 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (6 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (6 handlers)
[GIN-debug] GET    /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestQuestions-fm (6 handlers)
[GIN-debug] PUT    /api/tests/:test_id/active --> ziaacademy-backend/cmd/server/http.(*Handlers).ToggleTestActiveStatus-fm (6 handlers)
[GIN-debug] PUT    /api/tests/:test_id/results-disclosure --> ziaacademy-backend/cmd/server/http.(*Handlers).ToggleTestResultsDisclosure-fm (6 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (6 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (6 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (6 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (6 handlers)
[GIN-debug] GET    /api/tests/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (6 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (6 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (6 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (6 handlers)
[GIN-debug] PUT    /api/video-progress       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateVideoProgress-fm (6 handlers)
[GIN-debug] PUT    /api/study-material-progress --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateStudyMaterialProgress-fm (6 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (6 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (6 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (6 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (6 handlers)
[GIN-debug] POST   /api/orders               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateRazorpayOrder-fm (6 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
time=2025-09-01T20:05:16.603+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-09-01T20:05:16.603+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-09-01T20:05:16.603+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
