package db

import (
	"fmt"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCalculateSubjectAndTopicScores(t *testing.T) {
	// Skip if no test database is available
	if testDB == nil {
		t.Skip("Test database not available")
	}

	// Generate unique names to avoid conflicts
	timestamp := time.Now().UnixNano()
	mathSubjectName := fmt.Sprintf("SubjectScoreTestMath_%d", timestamp)
	physicsSubjectName := fmt.Sprintf("SubjectScoreTestPhysics_%d", timestamp)
	testName := fmt.Sprintf("SubjectScoreTest_%d", timestamp)

	// Clean up after test
	defer func() {
		testDB.Exec("DELETE FROM student_test_marks WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		testDB.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		testDB.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE ?)", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM users WHERE email LIKE ?", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM questions WHERE topic_id IN (SELECT id FROM topics WHERE name LIKE ?)", fmt.Sprintf("%%SubjectScoreTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE name LIKE ?)", fmt.Sprintf("%%SubjectScoreTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name IN (?, ?))", mathSubjectName, physicsSubjectName)
		testDB.Exec("DELETE FROM tests WHERE name = ?", testName)
		testDB.Exec("DELETE FROM test_types WHERE name LIKE ?", fmt.Sprintf("%%TestType_%d%%", timestamp))
		testDB.Exec("DELETE FROM difficulties WHERE name LIKE ?", fmt.Sprintf("%%TestDifficulty_%d%%", timestamp))
		testDB.Exec("DELETE FROM subjects WHERE name IN (?, ?)", mathSubjectName, physicsSubjectName)
	}()

	// Create test data
	subject1 := models.Subject{Name: mathSubjectName, DisplayName: "Mathematics"}
	err := testDB.Create(&subject1).Error
	require.NoError(t, err)

	subject2 := models.Subject{Name: physicsSubjectName, DisplayName: "Physics"}
	err = testDB.Create(&subject2).Error
	require.NoError(t, err)

	chapter1 := models.Chapter{Name: fmt.Sprintf("Algebra_%d", timestamp), DisplayName: "Algebra", SubjectID: subject1.ID}
	err = testDB.Create(&chapter1).Error
	require.NoError(t, err)

	chapter2 := models.Chapter{Name: fmt.Sprintf("Mechanics_%d", timestamp), DisplayName: "Mechanics", SubjectID: subject2.ID}
	err = testDB.Create(&chapter2).Error
	require.NoError(t, err)

	topic1 := models.Topic{Name: fmt.Sprintf("LinearEquations_%d", timestamp), ChapterID: chapter1.ID}
	err = testDB.Create(&topic1).Error
	require.NoError(t, err)

	topic2 := models.Topic{Name: fmt.Sprintf("QuadraticEquations_%d", timestamp), ChapterID: chapter1.ID}
	err = testDB.Create(&topic2).Error
	require.NoError(t, err)

	topic3 := models.Topic{Name: fmt.Sprintf("NewtonsLaws_%d", timestamp), ChapterID: chapter2.ID}
	err = testDB.Create(&topic3).Error
	require.NoError(t, err)

	// Create a difficulty (assuming it exists or create one)
	difficulty := models.Difficulty{Name: fmt.Sprintf("TestDifficulty_%d", timestamp)}
	err = testDB.Create(&difficulty).Error
	require.NoError(t, err)

	question1 := models.Question{Text: "Q1", TopicID: topic1.ID, DifficultyID: difficulty.ID, QuestionType: "mcq"}
	err = testDB.Create(&question1).Error
	require.NoError(t, err)

	question2 := models.Question{Text: "Q2", TopicID: topic2.ID, DifficultyID: difficulty.ID, QuestionType: "mcq"}
	err = testDB.Create(&question2).Error
	require.NoError(t, err)

	question3 := models.Question{Text: "Q3", TopicID: topic3.ID, DifficultyID: difficulty.ID, QuestionType: "mcq"}
	err = testDB.Create(&question3).Error
	require.NoError(t, err)

	// Create a test type (assuming it exists or create one)
	testType := models.TestType{Name: fmt.Sprintf("TestType_%d", timestamp)}
	err = testDB.Create(&testType).Error
	require.NoError(t, err)

	test := models.Test{Name: testName, TestTypeID: testType.ID}
	err = testDB.Create(&test).Error
	require.NoError(t, err)

	// Create test students
	user1 := models.User{
		FullName:    "Test Student 1",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: fmt.Sprintf("123456%04d", timestamp%10000),
		Role:        "student",
	}
	err = testDB.Create(&user1).Error
	require.NoError(t, err)

	student1 := models.Student{
		UserID:      user1.ID,
		ParentPhone: fmt.Sprintf("098765%04d", timestamp%10000),
		ParentEmail: fmt.Sprintf("<EMAIL>", timestamp),
	}
	err = testDB.Create(&student1).Error
	require.NoError(t, err)

	user2 := models.User{
		FullName:    "Test Student 2",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: fmt.Sprintf("123457%04d", timestamp%10000),
		Role:        "student",
	}
	err = testDB.Create(&user2).Error
	require.NoError(t, err)

	student2 := models.Student{
		UserID:      user2.ID,
		ParentPhone: fmt.Sprintf("098766%04d", timestamp%10000),
		ParentEmail: fmt.Sprintf("<EMAIL>", timestamp),
	}
	err = testDB.Create(&student2).Error
	require.NoError(t, err)

	// Create StudentTestMark records with subject marks instead of individual responses
	linearTopicName := fmt.Sprintf("LinearEquations_%d", timestamp)
	quadraticTopicName := fmt.Sprintf("QuadraticEquations_%d", timestamp)
	newtonsTopicName := fmt.Sprintf("NewtonsLaws_%d", timestamp)

	// Student 1: Math (Linear: 4, Quadratic: 5), Physics (Newton: 2)
	student1SubjectMarks := models.SubjectMarksMap{
		mathSubjectName: {
			PositiveMarks: 9, // 4 + 5
			NegativeMarks: 0,
			TopicMarks: map[string]models.TopicMarks{
				linearTopicName:    {PositiveMarks: 4, NegativeMarks: 0},
				quadraticTopicName: {PositiveMarks: 5, NegativeMarks: 0},
			},
		},
		physicsSubjectName: {
			PositiveMarks: 2,
			NegativeMarks: 0,
			TopicMarks: map[string]models.TopicMarks{
				newtonsTopicName: {PositiveMarks: 2, NegativeMarks: 0},
			},
		},
	}

	student1TestMark := models.StudentTestMark{
		StudentID:          int(student1.ID),
		TestID:             int(test.ID),
		TotalPositiveMarks: 11, // 4 + 5 + 2
		TotalNegativeMarks: 0,
		SubjectMarks:       student1SubjectMarks,
	}
	err = testDB.Create(&student1TestMark).Error
	require.NoError(t, err)

	// Student 2: Math (Linear: 3)
	student2SubjectMarks := models.SubjectMarksMap{
		mathSubjectName: {
			PositiveMarks: 3,
			NegativeMarks: 0,
			TopicMarks: map[string]models.TopicMarks{
				linearTopicName: {PositiveMarks: 3, NegativeMarks: 0},
			},
		},
	}

	student2TestMark := models.StudentTestMark{
		StudentID:          int(student2.ID),
		TestID:             int(test.ID),
		TotalPositiveMarks: 3,
		TotalNegativeMarks: 0,
		SubjectMarks:       student2SubjectMarks,
	}
	err = testDB.Create(&student2TestMark).Error
	require.NoError(t, err)

	// Create DbPlugin instance
	plugin := NewDbPlugin(testDB)

	// Test the function using the new approach
	studentMarks := []models.StudentTestMark{student1TestMark, student2TestMark}
	result := plugin.calculateSubjectScoresFromMarks(studentMarks)
	require.NotNil(t, result)

	// Verify Mathematics subject scores
	mathScores, exists := result[mathSubjectName]
	assert.True(t, exists, "Mathematics subject should exist in results")

	// Mathematics average: Student1(9) + Student2(3) / 2 students = 6.0
	assert.Equal(t, 6.0, mathScores.AverageScore, "Mathematics average score should be 6.0")

	// Verify topic scores within Mathematics
	assert.Contains(t, mathScores.TopicScores, linearTopicName)
	assert.Contains(t, mathScores.TopicScores, quadraticTopicName)

	// Linear Equations average: Student1(4) + Student2(3) / 2 students = 3.5
	assert.Equal(t, 3.5, mathScores.TopicScores[linearTopicName], "Linear Equations average should be 3.5")

	// Quadratic Equations average: Student1(5) / 1 student = 5.0
	assert.Equal(t, 5.0, mathScores.TopicScores[quadraticTopicName], "Quadratic Equations average should be 5.0")

	// Verify Physics subject scores
	physicsScores, exists := result[physicsSubjectName]
	assert.True(t, exists, "Physics subject should exist in results")

	// Physics average: Student1(2) / 1 student = 2.0
	assert.Equal(t, 2.0, physicsScores.AverageScore, "Physics average score should be 2.0")

	// Verify topic scores within Physics
	assert.Contains(t, physicsScores.TopicScores, newtonsTopicName)

	// Newton's Laws average: Student1(2) / 1 student = 2.0
	assert.Equal(t, 2.0, physicsScores.TopicScores[newtonsTopicName], "Newton's Laws average should be 2.0")
}

func TestCalculateSubjectAndTopicScoresEmptyResponses(t *testing.T) {
	// Skip if no test database is available
	if testDB == nil {
		t.Skip("Test database not available")
	}

	timestamp := time.Now().UnixNano()
	testName := fmt.Sprintf("EmptySubjectScoreTest_%d", timestamp)

	// Clean up after test
	defer func() {
		testDB.Exec("DELETE FROM tests WHERE name = ?", testName)
	}()

	// Create a test type (assuming it exists or create one)
	testType := models.TestType{Name: fmt.Sprintf("EmptyTestType_%d", timestamp)}
	err := testDB.Create(&testType).Error
	require.NoError(t, err)

	test := models.Test{Name: testName, TestTypeID: testType.ID}
	err = testDB.Create(&test).Error
	require.NoError(t, err)

	// Create DbPlugin instance
	plugin := NewDbPlugin(testDB)

	// Test the function with empty student marks
	emptyStudentMarks := []models.StudentTestMark{}
	result := plugin.calculateSubjectScoresFromMarks(emptyStudentMarks)
	require.NotNil(t, result)
	assert.Empty(t, result, "Result should be empty when no student marks exist")
}
