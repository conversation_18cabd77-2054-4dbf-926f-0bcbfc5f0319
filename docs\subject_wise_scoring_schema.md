# Subject-Wise Scoring Database Schema Documentation

## Overview

This document describes the database schema enhancements for subject-wise scoring and ranking functionality in the ZIA Academy backend system. The implementation allows for detailed tracking of student performance across subjects and topics within tests.

## Core Tables

### 1. `student_test_marks` Table

The primary table for storing aggregated test results with subject-wise breakdown.

```sql
CREATE TABLE student_test_marks (
    student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
    test_id INTEGER REFERENCES tests(id) ON DELETE CASCADE,
    total_positive_marks INTEGER DEFAULT 0 NOT NULL,
    total_negative_marks INTEGER DEFAULT 0 NOT NULL,
    final_marks INTEGER GENERATED ALWAYS AS (total_positive_marks - total_negative_marks) STORED NOT NULL,
    total_questions INTEGER DEFAULT 0 NOT NULL,  -- Total questions in the test
    attempted_questions INTEGER DEFAULT 0 NOT NULL,  -- Questions attempted by student
    unattempted_questions INTEGER DEFAULT 0 NOT NULL,  -- Questions not attempted by student
    correct_answers INTEGER DEFAULT 0 NOT NULL,  -- Questions answered correctly
    incorrect_answers INTEGER DEFAULT 0 NOT NULL,  -- Questions answered incorrectly
    subject_marks JSONB,  -- Subject-wise and topic-wise marks breakdown with question statistics
    PRIMARY KEY (student_id, test_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);
```

#### Key Features:
- **Generated Column**: `final_marks` is automatically calculated as `total_positive_marks - total_negative_marks`
- **JSONB Storage**: `subject_marks` stores hierarchical subject and topic-wise data
- **Composite Primary Key**: Ensures one record per student per test

### 2. `test_responses` Table

Enhanced to store individual response evaluations with calculated scores.

```sql
CREATE TABLE test_responses (
    id SERIAL PRIMARY KEY,
    student_id INT REFERENCES students(id) ON DELETE CASCADE,
    test_id INT REFERENCES tests(id) ON DELETE CASCADE,
    question_id INT REFERENCES questions(id) ON DELETE CASCADE,
    selected_option_ids INT[],
    response_text TEXT,
    calculated_score INTEGER,  -- Individual response score using section type rules
    is_correct BOOLEAN DEFAULT FALSE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);
```

### 3. `section_types` Table

Defines scoring rules for different question sections.

```sql
CREATE TABLE section_types (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    subject_id INTEGER NOT NULL REFERENCES subjects(id) ON DELETE CASCADE,
    question_count INTEGER NOT NULL,
    positive_marks NUMERIC NOT NULL,  -- Points for correct answers
    negative_marks NUMERIC NOT NULL,  -- Points deducted for incorrect answers
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    deleted_at timestamptz
);
```

## JSONB Structure for `subject_marks`

The `subject_marks` column stores data in the following hierarchical structure:

```json
{
  "Mathematics": {
    "positive_marks": 12,
    "negative_marks": 3,
    "total_questions": 5,
    "attempted_questions": 4,
    "unattempted_questions": 1,
    "correct_answers": 3,
    "incorrect_answers": 1,
    "topic_marks": {
      "Algebra": {
        "positive_marks": 8,
        "negative_marks": 1,
        "total_questions": 3,
        "attempted_questions": 2,
        "unattempted_questions": 1,
        "correct_answers": 2,
        "incorrect_answers": 0
      },
      "Geometry": {
        "positive_marks": 4,
        "negative_marks": 2,
        "total_questions": 2,
        "attempted_questions": 2,
        "unattempted_questions": 0,
        "correct_answers": 1,
        "incorrect_answers": 1
      }
    }
  },
  "Physics": {
    "positive_marks": 8,
    "negative_marks": 2,
    "total_questions": 3,
    "attempted_questions": 3,
    "unattempted_questions": 0,
    "correct_answers": 2,
    "incorrect_answers": 1,
    "topic_marks": {
      "Mechanics": {
        "positive_marks": 8,
        "negative_marks": 2,
        "total_questions": 3,
        "attempted_questions": 3,
        "unattempted_questions": 0,
        "correct_answers": 2,
        "incorrect_answers": 1
      }
    }
  }
}
```

## Performance Optimizations

### Indexes

1. **Test Rankings Index**:
   ```sql
   CREATE INDEX idx_test_final_marks_desc ON student_test_marks (test_id, final_marks DESC);
   ```
   - Optimizes ranking queries: `ORDER BY final_marks DESC`

2. **JSONB GIN Index**:
   ```sql
   CREATE INDEX idx_student_test_marks_subject_marks_gin ON student_test_marks USING GIN (subject_marks);
   ```
   - Enables efficient subject-wise queries
   - Supports operators: `?`, `@>`, `->`, `->>`, `#>`

3. **Composite Indexes**:
   ```sql
   CREATE INDEX idx_student_test_marks_student_test ON student_test_marks (student_id, test_id);
   CREATE INDEX idx_test_responses_student_test ON test_responses (student_id, test_id);
   ```

### Query Examples

#### 1. Get Test Rankings
```sql
SELECT 
    student_id,
    final_marks,
    ROW_NUMBER() OVER (ORDER BY final_marks DESC) as rank
FROM student_test_marks 
WHERE test_id = ? 
ORDER BY final_marks DESC;
```

#### 2. Subject-Wise Performance
```sql
SELECT 
    student_id,
    subject_marks->'Mathematics'->>'positive_marks' as math_positive,
    subject_marks->'Mathematics'->>'negative_marks' as math_negative
FROM student_test_marks 
WHERE test_id = ? 
AND subject_marks ? 'Mathematics';
```

#### 3. Topic-Wise Analysis
```sql
SELECT 
    student_id,
    subject_marks->'Mathematics'->'topic_marks'->'Algebra' as algebra_marks
FROM student_test_marks 
WHERE test_id = ?
AND subject_marks #> '{Mathematics,topic_marks,Algebra}' IS NOT NULL;
```

## Data Flow

### 1. Test Response Recording
1. Student submits test responses
2. Each response is evaluated using section type scoring rules
3. Individual scores are stored in `test_responses.calculated_score`
4. Responses are aggregated by subject and topic
5. Final aggregated data is stored in `student_test_marks`

### 2. Scoring Logic
- **Correct Answer**: `+positive_marks` from section type
- **Incorrect Answer**: `-negative_marks` from section type  
- **No Answer**: `0` points (no penalty)

### 3. Aggregation Process
1. Group responses by subject (via Question → Topic → Chapter → Subject)
2. Sum positive and negative marks per subject
3. Calculate topic-wise breakdowns within each subject
4. Store hierarchical data in JSONB format

## Migration Scripts

### Apply Schema Updates
```bash
# Run the comprehensive migration
psql -d your_database -f migrations/enhance_subject_wise_scoring_schema.sql

# Or apply individual migrations
psql -d your_database -f migrations/add_subject_marks_to_student_test_marks.sql
psql -d your_database -f migrations/add_subject_marks_gin_index.sql
```

### Verification
```sql
-- Check schema structure
SELECT column_name, data_type, is_generated 
FROM information_schema.columns
WHERE table_name = 'student_test_marks' 
AND column_name IN ('subject_marks', 'final_marks');

-- Verify indexes
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'student_test_marks';
```

## Best Practices

### 1. Query Optimization
- Use the GIN index for JSONB queries
- Leverage the generated `final_marks` column for rankings
- Use composite indexes for multi-column filters

### 2. Data Integrity
- The generated column ensures consistent final mark calculations
- Foreign key constraints maintain referential integrity
- Soft deletes preserve historical data

### 3. Monitoring
- Monitor JSONB query performance
- Consider materialized views for complex analytical queries
- Regular VACUUM and ANALYZE for optimal performance

## API Integration

The schema supports the following API endpoints:

- `POST /api/test-responses` - Records responses and calculates subject-wise scores
- `GET /api/tests/rankings/:test_id` - Returns ranked results with subject breakdowns
- `GET /api/test-responses/:test_id` - Returns individual student results with subject details

## Future Enhancements

Potential schema extensions:

1. **Time-based Analysis**: Add timestamp tracking for response patterns
2. **Difficulty Weighting**: Incorporate question difficulty in scoring
3. **Comparative Analytics**: Cross-test performance tracking
4. **Subject Percentiles**: Subject-specific ranking calculations
