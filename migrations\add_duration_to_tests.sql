-- Migration: Add duration column to tests table
-- This column stores test duration in minutes and is used for calculating ranking eligibility
-- Date: 2025-09-11

-- Add the duration column
ALTER TABLE tests 
ADD COLUMN IF NOT EXISTS duration INTEGER;

-- Add comment to document the column
COMMENT ON COLUMN tests.duration IS 'Test duration in minutes, used for calculating ranking eligibility upper bound';

-- Verify the migration
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'tests' 
  AND column_name = 'duration';

-- Show sample of tests with duration column
SELECT 
    id,
    name,
    from_time,
    to_time,
    duration,
    active
FROM tests 
LIMIT 10;
