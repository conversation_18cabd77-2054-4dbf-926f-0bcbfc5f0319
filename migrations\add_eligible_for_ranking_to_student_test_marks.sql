-- Migration: Add eligible_for_ranking column to student_test_marks table
-- This column tracks whether marks can be considered for ranking based on test timing
-- Date: 2025-09-11

-- Add the eligible_for_ranking column
ALTER TABLE student_test_marks 
ADD COLUMN IF NOT EXISTS eligible_for_ranking BOOLEAN DEFAULT FALSE NOT NULL;

-- Add index for efficient ranking queries
CREATE INDEX IF NOT EXISTS idx_student_test_marks_eligible_ranking 
ON student_test_marks (test_id, eligible_for_ranking, final_marks DESC) 
WHERE eligible_for_ranking = TRUE;

-- Update existing records to set eligible_for_ranking based on test timing
-- This will set eligible_for_ranking to TRUE for tests where:
-- 1. Both from_time and to_time are defined (not null)
-- 2. The current time falls between from_time and to_time
UPDATE student_test_marks 
SET eligible_for_ranking = TRUE
FROM tests 
WHERE student_test_marks.test_id = tests.id
  AND tests.from_time IS NOT NULL 
  AND tests.to_time IS NOT NULL
  AND NOW() BETWEEN tests.from_time AND tests.to_time;

-- Verify the migration
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN eligible_for_ranking = TRUE THEN 1 END) as eligible_records,
    COUNT(CASE WHEN eligible_for_ranking = FALSE THEN 1 END) as ineligible_records
FROM student_test_marks;

-- Show sample of updated records
SELECT 
    stm.student_id,
    stm.test_id,
    stm.eligible_for_ranking,
    t.name as test_name,
    t.from_time,
    t.to_time,
    NOW() as current_time
FROM student_test_marks stm
JOIN tests t ON stm.test_id = t.id
LIMIT 10;
